# UI Components

This directory contains all reusable UI components for the Ringerike Landskap website. Each component is organized in its own directory with an `index.tsx` file.

## Component Organization

All UI components follow a consistent organization pattern:

```
src/ui/
├── Button/
│   └── index.tsx
├── Card/
│   └── index.tsx
├── Container/
│   └── index.tsx
...
```

## Component Categories

The UI components are organized into the following categories:

### Core UI Components
- `Container`: Layout container for consistent spacing and width
- `Hero`: Hero section component for page headers
- `Logo`: Company logo component
- `ServiceAreaList`: List of service areas
- `SeasonalCTA`: Seasonal call-to-action component
- `Button`: Button and LinkButton components

### Layout Components
- `PageSection`: Section component for page layout
- `SectionHeading`: Heading component for sections
- `ContentGrid`: Grid layout for content

### Animation and Feedback Components
- `Intersection`: Component for intersection observer functionality
- `Notifications`: Notification system component
- `Skeleton`: Loading skeleton component
- `Transition`: Transition animation component
- `Loading`: Loading indicator component

### Data Display Components
- `Card`: Card component for displaying content
- `Icon`: Icon component

### Form Components
- `Form`: Form components (Input, Select, Textarea, etc.)

## Import Guidelines

When importing UI components, always use absolute imports with the `@/` prefix:

```tsx
// Good
import { Button } from '@/ui/Button';

// Bad
import { Button } from '../../ui/Button';
import { Button } from './Button';
```

## Adding New Components

When adding a new UI component:

1. Create a new directory in `src/ui/` with the component name in PascalCase
2. Create an `index.tsx` file in the directory
3. Export the component as default from the `index.tsx` file
4. Add the component export to `src/ui/index.ts` in the appropriate category
5. Use absolute imports with the `@/` prefix for all imports

## Component Structure

Each component should follow this basic structure:

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface ComponentProps {
  // Props definition
}

const Component: React.FC<ComponentProps> = ({ 
  // Props destructuring
}) => {
  // Component logic
  
  return (
    // JSX
  );
};

export default Component;
```
