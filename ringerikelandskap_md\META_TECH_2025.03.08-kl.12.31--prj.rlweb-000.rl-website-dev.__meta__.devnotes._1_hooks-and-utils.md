# Hooks and Utilities Documentation

## Custom Hooks

### useAnalytics
Tracks page views and user interactions.

```typescript
export const useAnalytics = () => {
  const location = useLocation();

  useEffect(() => {
    trackPageView(location.pathname + location.search);
  }, [location]);
};
```

### useProjects
Manages project data and loading state.

```typescript
export const useProjects = () => {
  const [projects, setProjects] = useState<ProjectType[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setProjects(recentProjects);
    setLoading(false);
  }, []);

  return { projects, loading };
};
```

### useServices
Manages service data and loading state.

```typescript
export const useServices = () => {
  const [items, setItems] = useState<ServiceType[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setItems(services);
    setLoading(false);
  }, []);

  return { services: items, loading };
};
```

### useForm
Handles form state, validation, and submission.

```typescript
export const useForm = <T extends Record<string, any>>({
  initialValues,
  validationRules,
  onSubmit
}: UseFormProps<T>) => {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<FormErrors<T>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form handling logic...

  return {
    values,
    errors,
    isSubmitting,
    handleChange,
    handleSubmit,
    reset
  };
};
```

### useMediaQuery
Detects if a media query matches.

```typescript
export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(() => 
    window.matchMedia(query).matches
  );

  useEffect(() => {
    const media = window.matchMedia(query);
    const listener = (e: MediaQueryListEvent) => setMatches(e.matches);
    
    media.addEventListener('change', listener);
    return () => media.removeEventListener('change', listener);
  }, [query]);

  return matches;
};
```

### useIntersectionObserver
Detects when an element enters the viewport.

```typescript
export const useIntersectionObserver = <T extends Element>({
  threshold = 0,
  root = null,
  rootMargin = '0px',
  freezeOnceVisible = false,
}: UseIntersectionObserverProps = {}): [RefObject<T>, boolean] => {
  const elementRef = useRef<T>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const element = elementRef.current;
    if (!element || (freezeOnceVisible && isVisible)) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold, root, rootMargin }
    );

    observer.observe(element);
    return () => observer.disconnect();
  }, [threshold, root, rootMargin, freezeOnceVisible, isVisible]);

  return [elementRef, isVisible];
};
```

## Utility Functions

### cn (classNames)
Combines class names with Tailwind CSS.

```typescript
import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
```

### getCurrentSeason
Determines the current season.

```typescript
export const getCurrentSeason = (): 'vinter' | 'vår' | 'sommer' | 'høst' => {
  const month = new Date().getMonth();
  if (month >= 2 && month <= 4) return 'vår';
  if (month >= 5 && month <= 7) return 'sommer';
  if (month >= 8 && month <= 10) return 'høst';
  return 'vinter';
};
```

### formatDate
Formats dates in Norwegian locale.

```typescript
export function formatDate(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('no-NO', {
    month: 'long',
    year: 'numeric'
  }).format(dateObj);
}
```

### slugify
Creates URL-friendly slugs.

```typescript
export function slugify(text: string): string {
  return text
    .toLowerCase()
    .replace(/æ/g, 'ae')
    .replace(/ø/g, 'o')
    .replace(/å/g, 'a')
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}
```

### validateEmail
Validates email addresses.

```typescript
export const validateEmail = (email: string): string | undefined => {
  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    return 'Ugyldig e-postadresse';
  }
};
```

### validatePhone
Validates Norwegian phone numbers.

```typescript
export const validatePhone = (phone: string): string | undefined => {
  if (!/^(\+47)?[2-9]\d{7}$/.test(phone.replace(/\s/g, ''))) {
    return 'Ugyldig telefonnummer (8 siffer)';
  }
};
```

### formatPhoneNumber
Formats Norwegian phone numbers.

```typescript
export function formatPhoneNumber(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 8) {
    return cleaned.replace(/(\d{3})(\d{2})(\d{3})/, '$1 $2 $3');
  }
  return phone;
}
```

### generateSEOMeta
Generates SEO metadata.

```typescript
export const generateSEOMeta = ({
  title,
  description = SITE_CONFIG.description,
  image = SITE_CONFIG.ogImage,
  type = 'website',
  path = ''
}: SEOProps) => {
  const fullTitle = title ? `${title} | ${SITE_CONFIG.name}` : SITE_CONFIG.name;
  const url = `${SITE_CONFIG.url}${path}`;

  return {
    title: fullTitle,
    meta: [
      {
        name: 'description',
        content: description
      },
      {
        property: 'og:title',
        content: fullTitle
      },
      // ... more meta tags
    ]
  };
};
```