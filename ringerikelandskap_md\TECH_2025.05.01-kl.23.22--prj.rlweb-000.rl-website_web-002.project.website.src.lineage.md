# Project Structure

This document outlines the organization and structure of the codebase.

## Directory Structure

- `src/app` - Application root shell and router
- `src/sections` - Chronologically ordered, self-contained sections
  - `10-home` - Home page and related components
  - `20-about` - About page and related components
  - `30-services` - Services pages and related components
  - `40-projects` - Projects pages and related components
  - `50-testimonials` - Testimonials pages and related components
  - `60-contact` - Contact page and related components
- `src/ui` - Global, atomic UI components (Button, Hero, Intersection, Notifications, etc.)
- `src/layout` - Shared layout components
- `src/lib` - Utility logic, API layer, config
- `src/hooks` - Custom React hooks

## Design Principles

1. **Chronological Organization**: Sections are prefixed with numbers to indicate their order in the user journey.
2. **Self-Contained Sections**: Each section contains all the components specific to that section.
3. **Shared Components**: Common UI elements are in the `ui` directory, layout components in the `layout` directory.
4. **Consistent Imports**: Use relative imports for clarity and maintainability.

## Migration Notes

This structure was implemented to improve code organization and maintainability. The previous structure had several issues:

1. Inconsistent page organization (some in directories, some at root level)
2. Duplicate files (e.g., both `pages/Services.tsx` and `pages/services/index.tsx`)
3. Mixed import patterns (both relative and alias imports)
4. Scattered feature components
5. Redundant component organization (components split between `components/ui` and `ui`)
6. Fragmented utility functions and hooks

The new structure addresses these issues by:

1. Providing a clear, consistent organization that follows the user journey through the application
2. Consolidating similar functionality in logical locations
3. Organizing UI components into a single directory with proper categorization
4. Grouping form-related components in a dedicated `ui/Form` directory
5. Consolidating utility functions, hooks, and types into their respective directories with proper index files
6. Using consistent import patterns throughout the codebase
