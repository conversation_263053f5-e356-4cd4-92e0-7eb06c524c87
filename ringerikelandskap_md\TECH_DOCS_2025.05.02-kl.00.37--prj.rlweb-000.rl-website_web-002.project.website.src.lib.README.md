# Library Directory Structure

This directory contains shared code that is used throughout the application. It is organized into subdirectories based on the type of code.

## Directory Structure

```
lib/
├── api/          # API client and related utilities
├── config/       # Configuration constants and settings
├── context/      # React context providers
├── hooks/        # React hooks
├── types/        # TypeScript type definitions
└── utils/        # Utility functions
```

## Usage Guidelines

### Hooks

All reusable React hooks should be placed in the `hooks` directory. Each hook should be in its own file, and exported from the `index.ts` file.

```typescript
// Good: Import from the hooks index
import { useData, useFormField } from '../lib/hooks';

// Avoid: Direct import from the hook file
// import { useData } from '../lib/hooks/useData';
```

### Types

Type definitions are organized into subdirectories based on their purpose:

- `types/components.ts`: Types for component props
- `types/content.ts`: Types for content data models
- `types/index.ts`: Re-exports all types

```typescript
// Good: Import specific types from their respective files
import { ProjectType } from '../lib/types/content';
import { ButtonProps } from '../lib/types/components';

// Also good: Import from the types index
import { ProjectType, ButtonProps } from '../lib/types';
```

### Utilities

Utility functions are organized into subdirectories based on their purpose:

- `utils/dom.ts`: DOM-related utilities
- `utils/formatting.ts`: Text and data formatting utilities
- `utils/images.ts`: Image processing utilities
- `utils/validation.ts`: Form validation utilities
- `utils/seo.ts`: SEO-related utilities
- `utils/seasonal.ts`: Seasonal content utilities
- `utils/analytics.ts`: Analytics utilities
- `utils/index.ts`: Re-exports all utilities

```typescript
// Good: Import specific utilities from their respective files
import { getWebPVersion } from '../lib/utils/images';
import { formatCurrency } from '../lib/utils/formatting';

// Also good: Import from the utils index
import { getWebPVersion, formatCurrency } from '../lib/utils';
```

## Adding New Code

When adding new code to the library:

1. Determine the appropriate subdirectory based on the type of code
2. Create a new file in that subdirectory
3. Export the code from the subdirectory's `index.ts` file
4. Import the code from the subdirectory's index file in your components

## Migration

If you're working with older code that doesn't follow this structure:

1. Run `node scripts/utils/update-imports.js` for guidance on updating imports
2. Update imports to use the new structure
3. Consider moving any utility functions, hooks, or types into the appropriate subdirectory

## Benefits of This Structure

- **Discoverability**: Easy to find code by its purpose
- **Maintainability**: Related code is grouped together
- **Consistency**: Standard patterns for importing and exporting code
- **Scalability**: Easy to add new code without creating clutter
- **Reusability**: Encourages sharing code across the application