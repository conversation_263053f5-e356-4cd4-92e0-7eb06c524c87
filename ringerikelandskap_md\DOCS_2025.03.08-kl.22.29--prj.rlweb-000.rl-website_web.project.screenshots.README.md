# Ringerike Landskap Website Screenshot Management

This directory contains automated screenshots of the Ringerike Landskap website, captured at different viewport sizes to ensure responsive design consistency.

## Directory Structure

-   **latest/**: Always contains the most recent screenshots for quick access
-   **YYYY-MM-DD_HH-MM-SS/**: Timestamped directories containing historical snapshots
-   **screenshot-report.html**: Interactive report to view and compare screenshots

## Viewport Sizes

Screenshots are captured at the following viewport sizes:

-   **Mobile**: 375×667px
-   **Tablet**: 768×1024px
-   **Desktop**: 1440×900px

## Pages Captured

The following pages are automatically captured:

-   Home (/)
-   About (/hvem-er-vi)
-   Services (/hva-vi-gjor)
-   Projects (/prosjekter)
-   Contact (/kontakt)

## Management Commands

The following npm scripts are available for managing screenshots:

```bash
# View help information
npm run screenshots

# Capture new screenshots
npm run screenshots:capture

# Clean up old screenshots (default: older than 7 days)
npm run screenshots:clean [days]

# Schedule automatic screenshot captures (default: every 24 hours)
npm run screenshots:schedule [hours]

# Remove scheduled screenshot captures
npm run screenshots:unschedule

# Clean up legacy screenshots (one-time migration)
npm run screenshots:cleanup-legacy

# Tidy up the screenshots directory (organize and optimize)
npm run screenshots:tidy

# Refresh screenshots (capture, tidy, and clean in one command)
npm run screenshots:refresh
```

## Configuration

The screenshot capture system is configured in `capture-website.js` with the following settings:

-   Maximum number of snapshot directories to keep: 5
-   HTML files are preserved alongside PNG screenshots
-   Screenshots are organized by viewport size
-   Automatic report generation

## Automated Cleanup

The system automatically removes old snapshots based on:

1. Maximum number of directories (keeps the 5 most recent)
2. Age-based cleanup (configurable, default is 7 days)

## Version Control

The screenshots directory is configured with a `.gitignore` file to prevent screenshots from being committed to the repository. Only the directory structure and README are preserved in version control.

## Viewing Screenshots

Open `screenshot-report.html` in your browser to view the latest screenshots in an interactive interface.

## Adding New Pages or Viewports

To add new pages or viewport sizes:

1. Edit `capture-website.js`
2. Add new entries to the `pages` or `viewports` arrays
3. Run `npm run screenshots:capture` to generate new screenshots
