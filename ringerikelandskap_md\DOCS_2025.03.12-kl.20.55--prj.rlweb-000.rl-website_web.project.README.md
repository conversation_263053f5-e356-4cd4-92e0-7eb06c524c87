## Dependency Visualization

This project includes a comprehensive dependency visualization system to help you understand the codebase structure and relationships between modules.

### Quick Commands

-   `npm run deps` - Show available dependency commands
-   `npm run deps:flow` - Generate and open the flow diagram (hierarchical visualization)
-   `npm run deps:d3` - Generate and open the D3 interactive graph
-   `npm run deps:all` - Generate all visualizations and open the dashboard
-   `npm run deps:audit` - Run a complete dependency audit with analysis
-   `npm run deps:analyze` - Analyze dependencies without generating visuals

### Prerequisites

The visualization system requires:

-   Node.js and npm (included in the project setup)
-   Graphviz (optional, for SVG-based visualizations) - [Install Guide](https://graphviz.org/download/)

Run `npm run deps:check` to verify your environment setup for visualizations.

### Documentation

For detailed information about the dependency visualization system, see [docs/dependency-visualization.md](docs/dependency-visualization.md).
