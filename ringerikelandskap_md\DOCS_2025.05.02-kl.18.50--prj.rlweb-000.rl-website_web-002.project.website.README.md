# Ringerike Landskap Website

This directory contains the website code for Ringerike Landskap.

## Overview

The website is built using React, TypeScript, and Vite. It uses a component-based architecture with a focus on reusability and maintainability. This separation from development tools provides several benefits:

1. **Clean separation of concerns**: Website code is separate from development tools.
2. **Simplified development**: All website files are in one place.
3. **Environment-specific configuration**: Different configurations can be used for development, staging, and production.
4. **Improved organization**: Clear separation of concerns makes the project easier to maintain.

## Directory Structure

```
website/
├── public/           # Static assets
│   ├── images/       # Images
│   └── ...           # Other static assets
└── src/              # Source code
    ├── app/          # Application root shell and router
    ├── sections/     # Chronologically ordered, self-contained sections
    │   ├── 10-home/  # Home page and related components
    │   ├── 20-about/ # About page and related components
    │   ├── 30-services/ # Services pages and related components
    │   ├── 40-projects/ # Projects pages and related components
    │   ├── 50-testimonials/ # Testimonials pages and related components
    │   └── 60-contact/ # Contact page and related components
    ├── ui/           # Global, atomic UI components
    ├── layout/       # Shared layout components
    ├── data/         # Static data (services, projects, testimonials)
    ├── lib/          # Utility logic, API layer, config
    ├── types/        # TypeScript definitions
    └── hooks/        # Custom React hooks
```

## Development

To start the development server:

```bash
npm run dev
```

## Building

To build the website for different environments:

```bash
# Development build
npm run build

# Staging build
npm run build:staging

# Production build
npm run build:production
```

## Deployment

To deploy the website, you can use any static hosting service. Simply upload the contents of the `dist` directory after building the website.

### Manual Deployment

1. Build the website: `npm run build` (or `build:staging` or `build:production`)
2. Upload the contents of the `dist` directory to your hosting service

### Automated Deployment

You can create deployment scripts in the `scripts` directory to automate the deployment process. For example:

```javascript
// Example deployment script (scripts/deploy.js)
import fs from 'fs';
import path from 'path';
// Add your deployment logic here
```

Then add a script to package.json:

```json
"scripts": {
  "deploy": "node scripts/deploy.js"
}
```

## Environment Configuration

The website uses environment-specific configuration files to handle different settings for development, staging, and production. These configurations are located in the `config/env` directory.

## CSS and Styling

The website uses Tailwind CSS for styling. The configuration is completely self-contained within the website directory:

- `tailwind.config.js` - Tailwind CSS configuration
- `postcss.config.js` - PostCSS configuration
- `src/index.css` - Main CSS file that imports Tailwind and other CSS files
- `src/styles/` - Directory containing additional CSS files:
  - `base.css` - Base styles
  - `utilities.css` - Utility classes
  - `animations.css` - Animation styles

When copying the website to a new location, make sure to install all dependencies including the Tailwind plugins:

```bash
npm install
```

## Using the Website Independently

This directory is designed to be self-contained and can be used independently of the parent project. Here's how to use it as a standalone project:

1. **Copy the directory**: Copy the entire `website` directory to a new location
2. **Install dependencies**: Run `npm install` in the new location
3. **Start development**: Run `npm run dev` to start the development server
4. **Build**: Run `npm run build` to build the website
5. **Preview build**: Run `npm run preview` to preview the built website

The build output will be in the `dist` directory within the website folder. All necessary configuration files are included in this directory, so it can function as a standalone project without any dependencies on the parent project.

### Build Output Structure

When you build the website, the output will be in the `dist` directory with the following structure:

```
dist/
├── assets/       # JavaScript and CSS files
├── images/       # Optimized images
├── index.html    # Main HTML file
├── robots.txt    # Robots file
└── site.webmanifest # Web app manifest
```

You can deploy this entire `dist` directory to any static hosting service.
