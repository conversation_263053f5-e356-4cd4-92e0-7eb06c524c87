# Active Context: Ringerike Landskap Website

## Current Focus Areas

### 1. Type System Consolidation
- **Status:** In Progress
- **Details:** 
  - Unified types directory created at `/src/types/`
  - Domain types separated into `/src/types/domain/`
  - Common types extracted to `/src/types/common.ts`
  - Component prop types defined in `/src/types/components.ts`
- **Remaining Work:**
  - Migrate remaining types from `/src/lib/types/`
  - Update import paths throughout the codebase
  - Remove deprecated type files

### 2. Hook Consolidation
- **Status:** In Progress
- **Details:**
  - New hook structure created at `/src/hooks/`
  - Domain-specific hooks in `/src/hooks/domain/`
  - UI-related hooks in `/src/hooks/ui/`
  - Legacy hooks preserved in `/src/hooks/legacy/`
- **Remaining Work:**
  - Migrate remaining hooks from `/src/lib/hooks/`
  - Resolve duplicate implementations (especially `useMediaQuery`)
  - Update import paths throughout the codebase

### 3. Feature Reorganization
- **Status:** Not Started
- **Details:**
  - Need to establish `/src/features/` structure
  - Plan to reorganize components from multiple directories
- **Next Steps:**
  - Create feature directories for Projects, Services, Testimonials
  - Move related components to appropriate feature directories
  - Consolidate duplicate implementations

### 4. API Layer Implementation
- **Status:** Not Started
- **Details:**
  - Need to extract API functions from lib/api and features
  - Create unified API client structure
- **Next Steps:**
  - Establish `/src/api/` directory
  - Create centralized client configuration
  - Add domain-specific endpoints

## Friction Points & Bottlenecks

### 1. Duplicate Testimonial Components
- **Issue:** Both `src/components/testimonials/` and `src/features/testimonials/` contain similar implementations
- **Impact:** Confusing developer experience, maintenance overhead
- **Resolution Plan:** Analyze both implementations, preserve the most robust, migrate references

### 2. Utility Function Fragmentation
- **Issue:** Utility functions spread across multiple directories
- **Impact:** Difficult to discover existing functionality, leads to duplication
- **Resolution Plan:** Catalog all utility functions, consolidate by category, update import paths

### 3. Import Path Complexity
- **Issue:** Inconsistent use of absolute/relative paths and aliases
- **Impact:** Makes refactoring more difficult, prone to errors
- **Resolution Plan:** Standardize path conventions, update tsconfig paths

### 4. Data vs. Content Separation
- **Issue:** Unclear boundaries between `/data/` and `/content/` directories
- **Impact:** Confusion about where to place and find static content
- **Resolution Plan:** Define clear ownership boundaries, consolidate into single pattern

## Current Consolidations

### 1. Type System Refactoring
```mermaid
flowchart TD
    A[Legacy Types] --> B[Analyze Related Types]
    B --> C[Define Domain Model]
    C --> D[Create Type Hierarchy]
    D --> E[Migrate Code]
    E --> F[Remove Legacy Types]
```

### 2. Hook Organization
```mermaid
flowchart TD
    A[Audit Hooks] --> B[Categorize by Purpose]
    B --> C[Define Implementation Standard]
    C --> D[Migrate Hooks to New Structure]
    D --> E[Update Import References]
    E --> F[Remove Legacy Hooks]
```

### 3. Component Consolidation
```mermaid
flowchart TD
    A[Identify Component Categories] --> B[Create Feature Directories]
    B --> C[Define Public Interfaces]
    C --> D[Move Components to Features]
    D --> E[Update Import References]
    E --> F[Remove Empty Directories]
