# System Patterns: Ringerikelandskap Website Refactor

## System Architecture

The Ringerikelandskap website refactor implements a modern React/TypeScript single-page application (SPA) architecture with the following key components:

### Frontend Architecture

```
Client Application (React/TypeScript)
├── Routing Layer (react-router-dom)
├── Component Layer
│   ├── Pages (Container Components)
│   └── UI Components (Presentational Components)
└── Visualization Layer (ReactFlow)
```

### Dependency Analysis Pipeline

```
Source Code → Dependency Analysis → JSON Data → Visualization
```

### Deployment Architecture

```
Development: Vite Dev Server → Browser
Production: Static Build → Express Server → Browser
```

## Key Technical Decisions

### 1. React + TypeScript + Vite Stack

**Decision**: Use React with TypeScript and Vite as the foundation
- **Rationale**: Modern performance, type safety, and developer experience
- **Implications**: Enables strong typing for component props and state, fast refresh during development, and optimized production builds

### 2. ReactFlow for Visualization

**Decision**: Use ReactFlow for dependency graph visualization
- **Rationale**: Specialized library for node-based interactive diagrams
- **Implications**: Provides built-in features like zoom, pan, node selection, and custom node styling

### 3. Express for Production Serving

**Decision**: Use Express.js to serve the production build
- **Rationale**: Simple yet flexible server for client-side routing support
- **Implications**: All routes redirect to index.html for SPA navigation

### 4. Static Analysis Approach

**Decision**: Pre-generate dependency data rather than real-time analysis
- **Rationale**: Better performance and simpler frontend implementation
- **Implications**: Dependency data must be regenerated when code changes

## Design Patterns in Use

### 1. Component Composition

The UI is structured using component composition patterns:
- **Container/Presentational Pattern**: Separation of data handling and UI rendering
- **Compound Components**: Related components grouped together functionally

### 2. Conditional Rendering

Visualization components use conditional rendering based on:
- View type (directory, subdirectory, file)
- Filter state
- Selected node state

### 3. Suspense for Code-Splitting

React Suspense is used for lazy loading components:
- Visualization components are loaded only when needed
- Loading fallbacks are provided for better UX

### 4. State Management

Local component state is used for UI controls and visualization options:
- React useState and useEffect for component state
- ReactFlow's specialized hooks (useNodesState, useEdgesState) for graph data

### 5. Custom Hooks (Potential)

While not explicitly visible in the current codebase, custom hooks could be implemented for:
- Dependency data fetching and processing
- View type management
- Filter state

## Component Relationships

### Main Application Flow

```
App → Routes → DependencyVisualizerPage → DependencyVisualizer → DetailedDependencyView (when node selected)
```

### Data Flow

```
1. Dependency data loaded from JSON
2. Data processed based on selected view type
3. Nodes and edges generated for ReactFlow
4. Rendered as interactive visualization
5. User interactions update view state
```

## Critical Implementation Paths

### 1. Dependency Data Processing

The core functionality depends on properly processing the dependency data:
- From raw dependency mapping to graph structure
- Handling different view levels (directory, subdirectory, file)
- Identifying and highlighting circular dependencies

### 2. ReactFlow Integration

The visualization relies on correct integration with ReactFlow:
- Proper node and edge creation
- Custom styling based on dependency types
- Interaction handling for selection and detailed views

### 3. Responsive UI

The application needs to maintain usability across different devices:
- Appropriate layout adjustments
- Touch interaction support
- Readable visualization at different screen sizes

## Architectural Principles

1. **Separation of Concerns**: Components have specific responsibilities
2. **Declarative UI**: React's declarative approach for UI representation
3. **Progressive Disclosure**: Information revealed as needed rather than all at once
4. **Responsive Design**: Adaptable to different viewport sizes
5. **Performance First**: Optimized rendering and data handling for complex visualizations
