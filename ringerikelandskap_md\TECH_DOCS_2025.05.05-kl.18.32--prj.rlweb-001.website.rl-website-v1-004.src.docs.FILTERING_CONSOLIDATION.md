# Filtering Consolidation

This document explains the consolidation of filtering utilities in the Ringerike Landskap website codebase.

## Overview

To improve maintainability and prevent code duplication, we have consolidated all filtering-related functionality into a single module: `src/lib/utils/filtering.ts`. This module now serves as the single source of truth for all filtering operations throughout the application.

## Changes Made

1. Moved all seasonal utilities from `src/lib/utils/seasonal.ts` to `src/lib/utils/filtering.ts`
2. Updated imports in components to use the consolidated module
3. Removed the deprecated `seasonal.ts` module completely
4. Cleaned up all verification scripts and components

## Benefits

- **Single Source of Truth**: All filtering logic is now in one place, making it easier to understand and maintain
- **Reduced Duplication**: Eliminates duplicate code and ensures consistent filtering behavior
- **Improved Discoverability**: Developers can find all filtering utilities in one place
- **Better Type Safety**: Consolidated utilities ensure consistent type usage
- **Reduced Technical Debt**: Removed deprecated code and temporary verification scripts

## Available Utilities

The consolidated `filtering.ts` module provides the following utilities:

### Seasonal Utilities

- `getCurrentSeason()`: Get the current season based on the current month
- `getSeasonDisplayName(season)`: Get the display name for a season
- `getEnglishSeasonName(season)`: Convert a Norwegian season name to English
- `serviceMatchesSeason(service, season, strictMode)`: Check if a service matches a season
- `projectMatchesSeason(project, season)`: Check if a project matches a season
- `getServiceMainCategory(service)`: Extract the main category from a service title

### Filtering Utilities

- `serviceMatchesCategory(service, category)`: Check if a service matches a category
- `serviceHasFeature(service, feature)`: Check if a service has a specific feature
- `filterServices(services, category, feature, season)`: Filter services based on criteria
- `projectMatchesCategory(project, category)`: Check if a project matches a category
- `projectMatchesLocation(project, location)`: Check if a project matches a location
- `projectHasTag(project, tag)`: Check if a project has a specific tag
- `filterProjects(projects, category, location, tag, season)`: Filter projects based on criteria
- `getUniqueFeatures(services, season, category)`: Get unique features from a list of services

## Future Improvements

In the future, we may consider further consolidation of filtering utilities, such as:

1. Creating specialized filtering hooks that encapsulate common filtering patterns
2. Adding more advanced filtering capabilities, such as multi-select filters
3. Improving performance of filtering operations for large datasets
