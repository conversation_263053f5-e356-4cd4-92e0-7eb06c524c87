# Ringerike Landskap Website

A modern, responsive website for Ringerike Landskap AS, a landscaping company based in Røyse, Norway.

## Features

-   Modern React-based frontend with TypeScript
-   Responsive design using Tailwind CSS
-   Fast loading with Vite
-   SEO optimized with metadata
-   Image optimization with WebP format (where available)

## Getting Started

```bash
npm install
```

3. Start the development server:

```bash
npm run dev
# or
yarn dev
```

4. Open your browser and navigate to http://localhost:5173/

## Project Structure

```
    # Project Context: Ringerike Landskap Website

    This project is a React-based web application for Ringerike Landskap, a Norwegian landscaping company located in the Ringerike region. The application is built using TypeScript, Tailwind CSS for styling, React Router for navigation, and Vite as the build tool. It features seasonal content adaptation and a focus on local SEO.

    ## Core Architecture

    - **Framework:** React with TypeScript
    - **Styling:** Tailwind CSS for responsive design
    - **Routing:** React Router
    - **State Management:** React hooks (local), Context API (global)
    - **Build Tool:** Vite
    - **Animation:** Framer Motion

    ## Key Features

    - Seasonal Content Adaptation
    - Geographic Authenticity Layer (locations: Røyse, Hønefoss, Hole, Jevnaker, Sundvollen, Vik)
    - Material-Feature Relationship System
    - Testimony-Project Connection Network

    ## File Structure Conventions

    - `src/components`: For reusable UI components.
    - `src/features`: For feature-specific components and logic.
    - `src/pages`: For top-level page components.
    - `src/hooks`: For custom React hooks.
    - `src/utils`: For helper functions and utilities.
    - `src/data`: For static data files (projects, services, testimonials).

    ## Component Hierarchy

    Ensure that the component hierarchy follows this pattern: UI Components → Feature Components → Page Components.

    ---

    ## Project Overview

    This is a React-based web application for Ringerike Landskap, a Norwegian landscaping company. The project uses TypeScript, Tailwind CSS, and follows a seasonal adaptation system.

    | Category             | Item                          | Description                                     |
    |----------------------|-------------------------------|-------------------------------------------------|
    |   Core Architecture  | Framework                     | React with TypeScript                           |
    |                      | Styling                       | Tailwind CSS for responsive design              |
    |                      | Routing                       | React Router for navigation                     |
    |                      | State Management              | React hooks (local), Context (global)           |
    |                      | Build Tool                    | Vite                                            |
    |   Key Features       | Seasonal Content Adaptation   | Dynamically adapts content based on seasons     |
    |                      | Geographic Authenticity Layer | Ensures accurate regional tagging               |
    |                      | Material-Feature Relationship System | Links materials to related features/projects |
    |                      | Testimony-Project Connection Network | Connects client testimonials with projects   |
    |   File Structure     | `src/components`              | Reusable UI components                          |
    |                      | `src/features`                | Feature-specific components and logic           |
    |                      | `src/pages`                   | Top-level page components                       |
    |                      | `src/hooks`                   | Custom React hooks                              |
    |                      | `src/utils`                   | Helper functions and utilities                  |
    |                      | `src/data`                    | Static data (projects, services, testimonials)  |

    ## Component Hierarchy
    UI Components → Feature Components → Page Components

    ---

    ## Coding Standards and Best Practices

    | Category               | Guidelines / Best Practices                                                            |
    |------------------------|----------------------------------------------------------------------------------------|
    | TypeScript Usage   | - Maintain strict TypeScript usage throughout the codebase.<br>- Use interface-driven development for component props and data structures. |
    | React Patterns     | - Prefer functional components with hooks over class components.<br>- Implement custom hooks for reusable logic (e.g., `useMediaQuery`, `useScrollPosition`).<br>- Use `React.memo` for pure functional components to optimize re-renders. |
    | State Management   | - Use React hooks for component-level state.<br>- Implement Context for global state (e.g., seasonal context, geographic context).<br>- Avoid prop drilling by leveraging Context or component composition. |
    | Performance Optimization | - Implement code splitting and lazy loading for non-critical components.<br>- Optimize images and assets for web delivery.<br>- Use Tailwind's purge option to minimize CSS bundle size. |
    | Seasonal Adaptation| - Always check `getCurrentSeason()` before modifying seasonal components.<br>- Ensure automatic detection and mapping of seasonal content is functioning.<br>- Verify that seasonal changes affect projects, services, and UI elements correctly. |
    | Geographic Authenticity | - Maintain location tags (`Røyse`, `Hønefoss`, `Hole`, `Jevnaker`, `Sundvollen`, `Vik`) when modifying components.<br>- Ensure service area consistency across all relevant components. |
    | Responsive Design  | - Follow a mobile-first approach using Tailwind breakpoints.<br>- Implement fluid typography and adaptive layouts for all screen sizes. |
    | Accessibility      | - Maintain proper heading hierarchy and ARIA attributes.<br>- Ensure keyboard navigation for all interactive elements.<br>- Use semantic HTML elements appropriately. |
    | SEO Optimization   | - Implement proper meta tags and structured data for each page.<br>- Optimize content for local SEO targeting the Ringerike region. |
    | Error Handling     | - Implement error boundaries for critical component trees.<br>- Use try/catch blocks for async operations.<br>- Log errors to a monitoring service (e.g., Sentry). |
    | Testing            | - Write unit tests using Jest and React Testing Library.<br>- Implement integration tests for critical user flows.<br>- Maintain a minimum of 80% test coverage. |
    | Documentation      | - Use JSDoc comments for functions and components.<br>- Maintain up-to-date README files in each major directory.<br>- Document complex business logic or algorithms. |
    | Version Control    | - Write clear, concise commit messages.<br>- Use feature branches and pull requests for all changes.<br>- Regularly update dependencies and address security vulnerabilities. |

    ---

    ## Coding Standards and Best Practices

    # Coding Standards

    ## TypeScript Usage
    - Maintain strict TypeScript usage throughout the codebase
    - Use interface-driven development for component props and data structures

    ## React Patterns
    - Prefer functional components with hooks over class components
    - Implement custom hooks for reusable logic (e.g., useMediaQuery, useScrollPosition)
    - Use React.memo for pure functional components to optimize re-renders

    ## State Management
    - Use React hooks for component-level state
    - Implement Context for global state (e.g., seasonal context, geographic context)
    - Avoid prop drilling by leveraging Context or component composition

    ## Performance Optimization
    - Implement code splitting and lazy loading for non-critical components
    - Optimize images and assets for web delivery
    - Use Tailwind's purge option to minimize CSS bundle size

    ## Seasonal Adaptation
    - Always check getCurrentSeason() before modifying seasonal components
    - Ensure automatic detection and mapping of seasonal content is functioning
    - Verify that seasonal changes affect projects, services, and UI elements correctly

    ## Geographic Authenticity
    - Maintain location tags (Røyse, Hønefoss, Hole, Jevnaker, Sundvollen, Vik, Bærum) when modifying components
    - Ensure service area consistency across all relevant components

    ## Responsive Design
    - Follow a mobile-first approach using Tailwind breakpoints
    - Implement fluid typography and adaptive layouts for all screen sizes

    ## Accessibility
    - Maintain proper heading hierarchy and ARIA attributes
    - Ensure keyboard navigation for all interactive elements
    - Use semantic HTML elements appropriately

    ## SEO Optimization
    - Implement proper meta tags and structured data for each page
    - Optimize content for local SEO targeting the Ringerike region

```

## Image Optimization

The website uses WebP images where available for better performance.

## Contact

Ringerike Landskap AS - [ringerikelandskap.no](https://ringerikelandskap.no)


### TODO: I

-   Filestructure
-   Enhance the understanding of the existing project layout and evaluate potential areas for automation.
-   Optimize the organization by minimizing the quantity of files without compromising the desired outcomes.
    Example sequence:
	- 1. Enhance comprehension of the current project structure and identify opportunities for automation.
	- 2. Streamline organization by reducing file volume without compromising desired results.
	- 3. Evaluate the revised version for any flaws and enhancements to uphold clarity, coherence, and impact.
	- 4. Specify the criteria for assessing flaws and enhancements, providing actionable critique and detailed justification.
