The attached filestructure represents the particular project they belong to, please propose an optimal way of consolidating the screenshot utility (to execute only on demand) into a single `website-snapshot.js`:

    ```
    ├── .gitignore
    ├── .gitkeep
    ├── capture-website.js
    ├── eslint.config.js
    ├── index.html
    ├── package-lock.json
    ├── package.json
    ├── postcss.config.js
    ├── rl-website-initial-notes.md
    ├── tailwind.config.js
    ├── tsconfig.app.json
    ├── tsconfig.json
    ├── tsconfig.node.json
    ├── vite.config.ts
    ├── public
    │   ├── robots.txt
    │   ├── site.webmanifest
    │   ├── sitemap.xml
    │   └── images
    │       ├── metadata.json
    │       ├── categorized
    │       │   ├── belegg
    │       │   ├── ferdigplen
    │       │   ├── hekk
    │       │   ├── kantstein
    │       │   ├── platting
    │       │   ├── stål
    │       │   ├── støttemur
    │       │   └── trapp-repo
    │       ├── site
    │       └── team
    ├── screenshots
    │   ├── .gitignore
    │   ├── .gitkeep
    │   ├── README.md
    │   ├── screenshot-report.html
    │   └── latest
    │       ├── .gitkeep
    │       ├── desktop
    │       │   ├── about-desktop.html
    │       │   ├── contact-desktop.html
    │       │   ├── home-desktop.html
    │       │   ├── projects-desktop.html
    │       │   └── services-desktop.html
    │       ├── mobile
    │       │   ├── about-mobile.html
    │       │   ├── contact-mobile.html
    │       │   ├── home-mobile.html
    │       │   ├── projects-mobile.html
    │       │   └── services-mobile.html
    │       └── tablet
    │           ├── about-tablet.html
    │           ├── contact-tablet.html
    │           ├── home-tablet.html
    │           ├── projects-tablet.html
    │           └── services-tablet.html
    ├── scripts
    │   ├── auto-snapshot.js
    │   ├── dev-with-snapshots.js
    │   ├── refresh-screenshots.js
    │   ├── screenshot-manager.js
    │   ├── tidy-screenshots.js
    │   ├── config
    │   │   └── screenshot-config.js
    │   └── utils
    │       └── screenshot-utils.js
    └── src
        ├── App.tsx
        ├── index.css
        ├── index.html
        ├── main.tsx
        ├── vite-env.d.ts
        ├── components
        │   ├── Navbar.tsx
        │   ├── ServiceCard.tsx
        │   ├── index.ts
        │   ├── common
        │   │   ├── Button.tsx
        │   │   ├── Container.tsx
        │   │   ├── Hero.tsx
        │   │   ├── ImageGallery.tsx
        │   │   ├── LocalExpertise.tsx
        │   │   ├── LocalServiceArea.tsx
        │   │   ├── Logo.tsx
        │   │   ├── SeasonalCTA.tsx
        │   │   ├── ServiceAreaList.tsx
        │   │   ├── WeatherAdaptedServices.tsx
        │   │   └── index.ts
        │   ├── contact
        │   │   └── ContactForm.tsx
        │   ├── layout
        │   │   ├── Footer.tsx
        │   │   ├── Header.tsx
        │   │   ├── Hero.tsx
        │   │   ├── Layout.tsx
        │   │   ├── Meta.tsx
        │   │   ├── Navbar.tsx
        │   │   └── index.ts
        │   ├── local
        │   │   ├── SeasonalGuide.tsx
        │   │   ├── ServiceAreaMap.tsx
        │   │   └── WeatherNotice.tsx
        │   ├── projects
        │   │   ├── ProjectCard.tsx
        │   │   ├── ProjectFilter.tsx
        │   │   ├── ProjectGallery.tsx
        │   │   └── ProjectGrid.tsx
        │   ├── seo
        │   │   └── TestimonialsSchema.tsx
        │   ├── services
        │   │   ├── Gallery.tsx
        │   │   └── ServiceCard.tsx
        │   ├── shared
        │   │   ├── ErrorBoundary.tsx
        │   │   ├── Elements
        │   │   │   ├── Card.tsx
        │   │   │   ├── Icon.tsx
        │   │   │   ├── Image.tsx
        │   │   │   ├── Link.tsx
        │   │   │   ├── Loading.tsx
        │   │   │   ├── index.ts
        │   │   │   └── Form
        │   │   │       ├── Input.tsx
        │   │   │       ├── Select.tsx
        │   │   │       ├── Textarea.tsx
        │   │   │       └── index.ts
        │   │   └── Layout
        │   │       ├── Layout.tsx
        │   │       └── index.ts
        │   └── ui
        │       ├── Button.tsx
        │       ├── Container.tsx
        │       ├── Hero.tsx
        │       ├── Intersection.tsx
        │       ├── Logo.tsx
        │       ├── Notifications.tsx
        │       ├── SeasonalCTA.tsx
        │       ├── ServiceAreaList.tsx
        │       ├── Skeleton.tsx
        │       ├── Transition.tsx
        │       └── index.ts
        ├── config
        │   ├── images.ts
        │   ├── routes.ts
        │   └── site.ts
        ├── content
        │   ├── index.ts
        │   ├── services
        │   │   └── index.ts
        │   ├── team
        │   │   └── index.ts
        │   └── testimonials
        │       └── index.ts
        ├── data
        │   ├── projects.ts
        │   ├── services.ts
        │   └── testimonials.ts
        ├── features
        │   ├── home.tsx
        │   ├── projects.tsx
        │   ├── services.tsx
        │   ├── team.tsx
        │   ├── testimonials.tsx
        │   ├── home
        │   │   ├── FilteredServicesSection.tsx
        │   │   ├── SeasonalProjectsCarousel.tsx
        │   │   ├── SeasonalServicesSection.tsx
        │   │   └── index.ts
        │   ├── projects
        │   │   ├── ProjectCard.tsx
        │   │   ├── ProjectFilter.tsx
        │   │   ├── ProjectGallery.tsx
        │   │   ├── ProjectGrid.tsx
        │   │   ├── ProjectsCarousel.tsx
        │   │   └── index.ts
        │   ├── services
        │   │   ├── ServiceCard.tsx
        │   │   ├── ServiceFeature.tsx
        │   │   ├── ServiceGrid.tsx
        │   │   ├── data.ts
        │   │   └── index.ts
        │   └── testimonials
        │       ├── AverageRating.tsx
        │       ├── Testimonial.tsx
        │       ├── TestimonialFilter.tsx
        │       ├── TestimonialSlider.tsx
        │       ├── TestimonialsSection.tsx
        │       ├── data.ts
        │       └── index.ts
        ├── hooks
        │   └── useData.ts
        ├── lib
        │   ├── constants.ts
        │   ├── content.ts
        │   ├── hooks.ts
        │   ├── index.ts
        │   ├── types.ts
        │   ├── utils.ts
        │   ├── api
        │   │   └── index.ts
        │   ├── config
        │   │   ├── images.ts
        │   │   ├── index.ts
        │   │   ├── paths.ts
        │   │   └── site.ts
        │   ├── context
        │   │   └── AppContext.tsx
        │   ├── hooks
        │   │   ├── images.ts
        │   │   ├── index.ts
        │   │   ├── useAnalytics.ts
        │   │   ├── useDebounce.ts
        │   │   ├── useEventListener.ts
        │   │   ├── useForm.ts
        │   │   ├── useFormField.ts
        │   │   ├── useIntersectionObserver.ts
        │   │   ├── useLocalStorage.ts
        │   │   └── useMediaQuery.ts
        │   ├── types
        │   │   ├── common.ts
        │   │   ├── components.ts
        │   │   ├── content.ts
        │   │   └── index.ts
        │   └── utils
        │       ├── analytics.ts
        │       ├── date.ts
        │       ├── images.ts
        │       ├── index.ts
        │       ├── seo.ts
        │       └── validation.ts
        ├── pages
        │   ├── ProjectDetail.tsx
        │   ├── Projects.tsx
        │   ├── ServiceDetail.tsx
        │   ├── Services.tsx
        │   ├── TestimonialsPage.tsx
        │   ├── about
        │   │   └── index.tsx
        │   ├── contact
        │   │   └── index.tsx
        │   ├── home
        │   │   └── index.tsx
        │   ├── projects
        │   │   ├── detail.tsx
        │   │   └── index.tsx
        │   ├── services
        │   │   ├── detail.tsx
        │   │   └── index.tsx
        │   └── testimonials
        │       └── index.tsx
        ├── styles
        │   ├── animations.css
        │   ├── base.css
        │   └── utilities.css
        ├── types
        │   ├── content.ts
        │   └── index.ts
        └── utils
            └── imageLoader.ts
    ```
