# Ringerike Landskap - TL;DR

## Core Architecture

```
src/
├── components/        # UI building blocks
│   ├── ui/            # Base components
│   ├── layout/        # Structure components
│   └── features/      # Feature-specific components
│
├── features/          # Business logic
│   ├── projects/      # Project-related features
│   ├── services/      # Service-related features
│   └── testimonials/  # Testimonial features
│
├── lib/               # Core utilities
│   ├── hooks/         # React hooks
│   ├── utils/         # Helper functions
│   └── types/         # TypeScript types
│
└── pages/             # Route components
```

## Key Concepts

1. **Seasonal Adaptation**
   - Content changes based on current season
   - Affects projects, services, and UI elements
   - Automatic detection and mapping

2. **Component Hierarchy**
   - UI Components → Feature Components → Page Components
   - Composition over inheritance
   - Reusable building blocks

3. **Data Flow**
   - Static data in `/data`
   - Props down, events up
   - Context for global state
   - Custom hooks for logic

4. **Responsive Design**
   - Mobile-first approach
   - Tailwind breakpoints
   - Fluid typography
   - Adaptive layouts

5. **Type Safety**
   - TypeScript throughout
   - Strict type checking
   - Interface-driven development

## Core Features

1. **Projects**
   - Filtering by category/location/season
   - Detailed views
   - Image galleries
   - Related services

2. **Services**
   - Seasonal recommendations
   - Feature highlights
   - Related projects
   - Contact CTAs

3. **Testimonials**
   - Rating system
   - Filtering
   - Seasonal relevance
   - Social proof

## Key Patterns

1. **Component Patterns**
   ```tsx
   <Container>
     <Hero />
     <ServiceGrid services={seasonalServices} />
     <TestimonialsSection />
   </Container>
   ```

2. **Hook Patterns**
   ```tsx
   const { projects } = useProjects();
   const isDesktop = useMediaQuery('(min-width: 1024px)');
   ```

3. **Data Patterns**
   ```typescript
   interface ProjectType {
     id: string;
     title: string;
     category: string;
     season: string;
   }
   ```

## Quick Reference

- **Seasonal Logic**: `getCurrentSeason()`
- **Routing**: React Router v6
- **Styling**: Tailwind CSS
- **State**: React Context + Hooks
- **Build**: Vite
- **Deployment**: Netlify

## File Naming

- Components: PascalCase (`Button.tsx`)
- Utilities: camelCase (`imageLoader.ts`)
- Types: PascalCase (`ProjectType.ts`)
- Hooks: camelCase (`useProjects.ts`)

## Best Practices

1. **Components**
   - Single responsibility
   - Prop validation
   - Error boundaries
   - Accessibility

2. **Performance**
   - Lazy loading
   - Image optimization
   - Code splitting
   - Memoization

3. **Maintenance**
   - Clear documentation
   - Consistent patterns
   - Type safety
   - Unit tests