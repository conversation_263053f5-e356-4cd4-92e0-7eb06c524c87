# Dir `components`

*Files marked `[-]` are shown in structure but not included in content.

### File Structure

```
├── (sections)
│   ├── coverage-area
│   │   └── CoverageArea.tsx
│   ├── hero
│   │   └── Hero.tsx
│   ├── projects
│   │   ├── ProjectCard.tsx
│   │   └── ProjectsCarousel.tsx
│   ├── seasonal-planning
│   │   └── SeasonalPlanning.tsx
│   ├── services
│   │   ├── ServiceCard.tsx
│   │   ├── ServiceFeature.tsx
│   │   └── Services.tsx
│   └── testimonials
│       └── Testimonials.tsx
├── ContactForm.tsx
├── Navbar.tsx
├── ServiceCard.tsx
├── common
│   ├── Button.tsx
│   ├── Container.tsx
│   ├── Hero.tsx
│   ├── ImageGallery.tsx
│   ├── LocalExpertise.tsx
│   ├── LocalServiceArea.tsx
│   ├── Logo.tsx
│   ├── SeasonalCTA.tsx
│   ├── ServiceAreaList.tsx
│   └── WeatherAdaptedServices.tsx
├── contact
│   └── ContactForm.tsx
├── index.ts
├── layout
│   ├── Footer.tsx
│   ├── Hero.tsx
│   ├── Layout.tsx
│   ├── Meta.tsx
│   └── Navbar.tsx
├── local
│   ├── SeasonalGuide.tsx
│   ├── ServiceAreaMap.tsx
│   └── WeatherNotice.tsx
├── projects
│   ├── ProjectCard.tsx
│   ├── ProjectFilter.tsx
│   ├── ProjectGallery.tsx
│   └── ProjectGrid.tsx
├── seo
│   └── TestimonialsSchema.tsx
├── services
│   ├── Gallery.tsx
│   └── ServiceCard.tsx
├── shared
│   ├── Elements
│   │   ├── Card.tsx
│   │   ├── Form
│   │   │   ├── Input.tsx
│   │   │   ├── Select.tsx
│   │   │   ├── Textarea.tsx
│   │   │   └── index.ts
│   │   ├── Icon.tsx
│   │   ├── Image.tsx
│   │   ├── Link.tsx
│   │   ├── Loading.tsx
│   │   └── index.ts
│   ├── ErrorBoundary.tsx
│   └── Layout
│       ├── Layout.tsx
│       └── index.ts
└── ui
    ├── Button.tsx
    ├── Container.tsx
    ├── Hero.tsx
    ├── Intersection.tsx
    ├── Logo.tsx
    ├── Notifications.tsx
    ├── Skeleton.tsx
    └── Transition.tsx
```


#### `(sections)\coverage-area\CoverageArea.tsx`

```tsx
import React from 'react';
import { Container } from '@/components/ui/Container';
import { MapPin, Navigation } from 'lucide-react';
import { SERVICE_AREAS } from '@/lib/constants';
import { cn } from '@/lib/utils';

const CoverageArea = () => {
  return (
    <section className="py-12 bg-gray-50">
      <Container>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          <div>
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-green-100 rounded-lg">
                <Navigation className="w-6 h-6 text-green-500" />
              </div>
              <h2 className="text-2xl font-semibold">Vårt dekningsområde</h2>
            </div>
            
            <p className="text-gray-600 mb-8 leading-relaxed">
              Med base på Røyse betjener vi hele Ringerike-regionen med lokalkunnskap 
              og personlig service. Vi kjenner hver bakketopp og hvert jordsmonn i området.
            </p>

            <div className="space-y-3">
              {SERVICE_AREAS.map((area) => (
                <div 
                  key={area.city}
                  className={cn(
                    "flex items-center justify-between p-3 rounded-lg transition-all duration-300",
                    area.isBase 
                      ? "bg-green-50 border border-green-100" 
                      : "hover:bg-white hover:shadow-sm"
                  )}
                >
                  <div className="flex items-center gap-3">
                    <MapPin className={cn(
                      "w-4 h-4",
                      area.isBase ? "text-green-500" : "text-gray-400"
                    )} />
                    <span className={cn(
                      "text-sm",
                      area.isBase && "font-medium"
                    )}>
                      {area.city}
                    </span>
                  </div>
                  <span className="text-sm text-gray-500">{area.distance}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="relative h-[400px] rounded-lg overflow-hidden shadow-lg">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2034.7098040861837!2d10.2558!3d60.0558!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNjDCsDAzJzIwLjgiTiAxMMKwMTUnMjAuOSJF!5e0!3m2!1sno!2sno!4v1647856231968!5m2!1sno!2sno"
              width="100%"
              height="100%"
              style={{ border: 0 }}
              allowFullScreen
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              title="Kart over vårt dekningsområde"
              className="absolute inset-0"
            />
            <div 
              className="absolute inset-0 pointer-events-none"
              style={{
                boxShadow: 'inset 0 0 20px rgba(0,0,0,0.1)'
              }}
            />
          </div>
        </div>
      </Container>
    </section>
  );
};

export default CoverageArea;```


#### `(sections)\hero\Hero.tsx`

```tsx
import React from 'react';
import { HeroProps } from '@/types';
import { Container } from '@/components/ui/Container';
import { Button } from '@/components/ui/Button';
import { ArrowRight } from 'lucide-react';

const Hero: React.FC<HeroProps> = ({ 
  title = "Anleggsgartner i Ringerike",
  subtitle = "Med base på Røyse skaper vi varige uterom tilpasset Ringerikes unike terreng og klima. Vi kjenner hver bakketopp og hvert jordsmonn i regionen.",
  backgroundImage 
}) => {
  return (
    <section 
      className="relative min-h-[480px] sm:min-h-[580px] flex items-center justify-center text-white overflow-hidden"
      style={{
        backgroundImage: `url(${backgroundImage || 'https://images.unsplash.com/photo-1558904541-efa843a96f01?auto=format&fit=crop&q=80'})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      }}
      role="banner"
      aria-label="Hero section"
    >
      {/* Parallax overlay */}
      <div 
        className="absolute inset-0 bg-gradient-to-b from-black/30 via-black/50 to-black/70"
        style={{ transform: 'translateZ(0)' }}
        aria-hidden="true"
      />

      <Container className="relative z-10">
        <div className="max-w-3xl mx-auto text-center">
          <h1 
            className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 tracking-tight" 
            itemProp="headline"
          >
            {title}
          </h1>
          {subtitle && (
            <p 
              className="text-lg sm:text-xl max-w-2xl mx-auto leading-relaxed mb-8 text-gray-100" 
              itemProp="description"
            >
              {subtitle}
            </p>
          )}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              to="/kontakt"
              size="lg"
              className="group"
            >
              Book gratis befaring
              <ArrowRight className="ml-2 w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
            </Button>
            <Button 
              to="/hva-vi-gjor"
              variant="outline"
              size="lg"
              className="bg-white/10 backdrop-blur-sm border-white text-white hover:bg-white/20"
            >
              Se våre tjenester
            </Button>
          </div>
        </div>
      </Container>

      {/* Animated gradient overlay */}
      <div 
        className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-transparent opacity-60 mix-blend-overlay"
        style={{ 
          animation: 'gradient 8s ease-in-out infinite',
          backgroundSize: '200% 200%'
        }}
        aria-hidden="true"
      />

      <style jsx>{`
        @keyframes gradient {
          0% { background-position: 0% 50% }
          50% { background-position: 100% 50% }
          100% { background-position: 0% 50% }
        }
      `}</style>
    </section>
  );
};

export default Hero;```


#### `(sections)\projects\ProjectCard.tsx`

```tsx
import React from 'react';
import { MapPin, Calendar } from 'lucide-react';
import { ProjectType } from '@/types';
import { cn } from '@/lib/utils';

interface ProjectCardProps {
  project: ProjectType;
  onProjectClick: (id: string) => void;
  className?: string;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ project, onProjectClick, className }) => {
  return (
    <button 
      onClick={() => onProjectClick(project.id)}
      className={cn(
        "relative w-full h-[280px] md:h-[320px] text-left",
        "transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",
        className
      )}
    >
      <div
        className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
        style={{ backgroundImage: `url(${project.image})` }}
        aria-hidden="true"
      />
      <div 
        className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent"
        aria-hidden="true"
      />

      <div className="absolute bottom-0 left-0 right-0 p-6">
        <div className="flex flex-wrap items-center gap-2 mb-2">
          <span className="inline-flex items-center gap-1.5 bg-green-500 text-white px-2.5 py-1 rounded-full text-xs sm:text-sm font-medium">
            {project.category}
          </span>
          <div className="flex items-center gap-1.5 text-white/90">
            <MapPin className="w-4 h-4" />
            <span className="text-xs sm:text-sm">{project.location}</span>
          </div>
          <div className="flex items-center gap-1.5 text-white/90">
            <Calendar className="w-4 h-4" />
            <span className="text-xs sm:text-sm">{project.completionDate}</span>
          </div>
        </div>
        
        <h3 className="text-lg sm:text-xl font-medium mb-2 text-white line-clamp-1">
          {project.title}
        </h3>
        <p className="text-gray-200 mb-3 text-sm leading-relaxed line-clamp-2">
          {project.description}
        </p>
      </div>

      {/* Hidden text for screen readers */}
      <span className="sr-only">
        Se detaljer om prosjektet {project.title}
      </span>
    </button>
  );
};

export default ProjectCard;```


#### `(sections)\projects\ProjectsCarousel.tsx`

```tsx
import React, { useState, useEffect, useCallback } from 'react';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Container } from '@/components/ui/Container';
import ProjectCard from './ProjectCard';
import { useProjects } from '@/hooks/useProjects';

const ProjectsCarousel = () => {
  const navigate = useNavigate();
  const { projects, loading } = useProjects();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [touchStart, setTouchStart] = useState<number | null>(null);

  const handleProjectClick = useCallback((id: string) => {
    navigate(`/prosjekter/${id}`);
  }, [navigate]);

  const handlePrevious = useCallback(() => {
    if (isAnimating || !projects.length) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev === 0 ? projects.length - 1 : prev - 1));
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating, projects.length]);

  const handleNext = useCallback(() => {
    if (isAnimating || !projects.length) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev === projects.length - 1 ? 0 : prev + 1));
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating, projects.length]);

  useEffect(() => {
    const interval = setInterval(handleNext, 5000);
    return () => clearInterval(interval);
  }, [handleNext]);

  // Touch handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.touches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!touchStart) return;
    
    const touchEnd = e.touches[0].clientX;
    const diff = touchStart - touchEnd;

    if (Math.abs(diff) > 50) {
      if (diff > 0) {
        handleNext();
      } else {
        handlePrevious();
      }
      setTouchStart(null);
    }
  };

  if (loading) {
    return (
      <Container>
        <div className="h-[400px] bg-gray-100 rounded-lg animate-pulse" />
      </Container>
    );
  }

  if (!projects.length) {
    return null;
  }

  return (
    <section className="py-12">
      <Container>
        <div className="mb-8">
          <h2 className="text-2xl sm:text-3xl font-semibold mb-4">
            Våre siste <span className="text-green-500">prosjekter</span>
          </h2>
          <p className="text-gray-600 max-w-2xl">
            Se hvordan vi har hjulpet kunder i Ringerike-regionen med å skape 
            vakre og funksjonelle uterom.
          </p>
        </div>

        <div 
          className="relative group"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
        >
          <div className="overflow-hidden rounded-lg">
            <div 
              className="relative transition-transform duration-500 ease-in-out"
              style={{ 
                transform: `translateX(-${currentIndex * 100}%)`,
                display: 'flex' 
              }}
            >
              {projects.map((project) => (
                <div 
                  key={project.id} 
                  className="w-full flex-shrink-0"
                >
                  <ProjectCard 
                    project={project}
                    onProjectClick={handleProjectClick}
                  />
                </div>
              ))}
            </div>
          </div>

          <button
            onClick={handlePrevious}
            className="absolute -left-6 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/90 shadow-lg text-gray-700 hover:bg-white hover:text-gray-900 transition-all duration-300 opacity-0 group-hover:opacity-100 group-hover:-left-8 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            aria-label="Forrige prosjekt"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>

          <button
            onClick={handleNext}
            className="absolute -right-6 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/90 shadow-lg text-gray-700 hover:bg-white hover:text-gray-900 transition-all duration-300 opacity-0 group-hover:opacity-100 group-hover:-right-8 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            aria-label="Neste prosjekt"
          >
            <ArrowRight className="w-5 h-5" />
          </button>

          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
            {projects.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === currentIndex 
                    ? 'bg-white w-4' 
                    : 'bg-white/50 hover:bg-white/70'
                }`}
                aria-label={`Gå til prosjekt ${index + 1}`}
                aria-current={index === currentIndex}
              />
            ))}
          </div>
        </div>
      </Container>
    </section>
  );
};

export default ProjectsCarousel;```


#### `(sections)\seasonal-planning\SeasonalPlanning.tsx`

```tsx
import React from 'react';
import { Container } from '@/components/ui/Container';
import { Button } from '@/components/ui/Button';
import { Cloud, Sun, CloudRain, Calendar } from 'lucide-react';

const getCurrentSeason = () => {
  const month = new Date().getMonth();
  if (month >= 2 && month <= 4) return 'vår';
  if (month >= 5 && month <= 7) return 'sommer';
  if (month >= 8 && month <= 10) return 'høst';
  return 'vinter';
};

const SeasonalPlanning = () => {
  const currentSeason = getCurrentSeason();
  
  const seasons = [
    {
      name: 'Vår',
      icon: <Cloud className="w-6 h-6" />,
      services: [
        'Vårklargjøring av hager',
        'Planting og såing',
        'Beskjæring av busker og trær'
      ],
      active: currentSeason === 'vår'
    },
    {
      name: 'Sommer',
      icon: <Sun className="w-6 h-6" />,
      services: [
        'Vanningsløsninger',
        'Vedlikehold av grøntarealer',
        'Etablering av nye bed'
      ],
      active: currentSeason === 'sommer'
    },
    {
      name: 'Høst',
      icon: <CloudRain className="w-6 h-6" />,
      services: [
        'Høstklargjøring',
        'Planting av løk',
        'Beskyttelse mot frost'
      ],
      active: currentSeason === 'høst'
    }
  ];

  return (
    <section className="py-12">
      <Container>
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 text-sm text-gray-600 mb-4">
            <Calendar className="w-4 h-4" />
            <span>Nåværende sesong: {currentSeason}</span>
          </div>
          <h2 className="text-2xl sm:text-3xl font-semibold mb-4">
            Planlegg for <span className="text-green-500">alle sesonger</span>
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Vi tilpasser våre tjenester etter årstidene for å sikre at ditt uterom 
            er vakkert og funksjonelt året rundt.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          {seasons.map((season) => (
            <div 
              key={season.name}
              className={cn(
                "p-6 rounded-lg transition-all duration-300",
                season.active 
                  ? "bg-green-50 border border-green-100" 
                  : "bg-white hover:shadow-md"
              )}
            >
              <div className="flex items-center gap-3 mb-4">
                <div className={cn(
                  "p-2 rounded-lg",
                  season.active ? "bg-green-100" : "bg-gray-100"
                )}>
                  {React.cloneElement(season.icon, {
                    className: cn(
                      "w-6 h-6",
                      season.active ? "text-green-500" : "text-gray-500"
                    )
                  })}
                </div>
                <h3 className="text-lg font-medium">{season.name}</h3>
              </div>
              
              <ul className="space-y-3 mb-6">
                {season.services.map((service, index) => (
                  <li 
                    key={index}
                    className="flex items-start gap-2 text-sm text-gray-600"
                  >
                    <span className="w-1.5 h-1.5 bg-green-500 rounded-full mt-1.5" />
                    {service}
                  </li>
                ))}
              </ul>

              {season.active && (
                <Button 
                  to="/kontakt"
                  variant="primary"
                  className="w-full"
                >
                  Book befaring nå
                </Button>
              )}
            </div>
          ))}
        </div>

        <div className="text-center">
          <Button 
            to="/hva-vi-gjor"
            variant="outline"
            size="lg"
          >
            Se alle våre tjenester
          </Button>
        </div>
      </Container>
    </section>
  );
};

export default SeasonalPlanning;```


#### `(sections)\services\ServiceCard.tsx`

```tsx
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import { ServiceType } from '@/types';
import { cn } from '@/lib/utils';

interface ServiceCardProps {
  service: ServiceType;
  className?: string;
}

export const ServiceCard: React.FC<ServiceCardProps> = ({ service, className }) => {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate(`/tjenester/${service.id}`);
  };

  return (
    <button 
      onClick={handleClick}
      className={cn(
        "group relative w-full h-[280px] overflow-hidden rounded-lg shadow-sm text-left",
        "transition-all duration-300 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",
        className
      )}
    >
      <div 
        className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
        style={{ backgroundImage: `url(${service.image})` }}
        aria-hidden="true"
      />
      <div 
        className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent"
        aria-hidden="true"
      />
      
      <div className="absolute bottom-0 left-0 right-0 p-6 transform transition-transform duration-300">
        <h3 className="text-xl font-semibold text-white mb-2">
          {service.title}
        </h3>
        <p className="text-gray-200 text-sm mb-4 line-clamp-2">
          {service.description}
        </p>
        <span 
          className="inline-flex items-center gap-1.5 text-green-400 text-sm font-medium group-hover:gap-2.5"
          aria-hidden="true"
        >
          Les mer
          <ArrowRight className="w-4 h-4 transition-all duration-300" />
        </span>
      </div>

      {/* Hidden text for screen readers */}
      <span className="sr-only">
        Les mer om {service.title.toLowerCase()}
      </span>
    </button>
  );
};```


#### `(sections)\services\ServiceFeature.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface ServiceFeatureProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  className?: string;
}

export const ServiceFeature: React.FC<ServiceFeatureProps> = ({
  icon,
  title,
  description,
  className
}) => {
  return (
    <div className={cn(
      "text-center p-6 bg-white rounded-lg shadow-sm transition-all duration-300 hover:shadow-md",
      className
    )}>
      <div className="inline-block p-3 bg-green-50 rounded-full mb-4 text-green-500">
        {icon}
      </div>
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <p className="text-sm text-gray-600">{description}</p>
    </div>
  );
};```


#### `(sections)\services\Services.tsx`

```tsx
import React from 'react';
import { Container } from '@/components/ui/Container';
import { ServiceCard } from './ServiceCard';
import { ServiceFeature } from './ServiceFeature';
import { useServices } from '@/hooks/useServices';
import { Shield, MapPin, Droplet } from 'lucide-react';

const Services = () => {
  const { services, loading } = useServices();

  const features = [
    {
      icon: <MapPin className="w-6 h-6" />,
      title: 'Lokalkunnskap',
      description: 'Med base på Røyse kjenner vi Ringerikes unike terreng og jordforhold.'
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: 'Tilpassede løsninger',
      description: 'Skreddersydde løsninger for ditt uterom og lokale forhold.'
    },
    {
      icon: <Droplet className="w-6 h-6" />,
      title: 'Klimatilpasset',
      description: 'Løsninger som tåler både tørre somre og våte høstdager.'
    }
  ];

  if (loading) {
    return <ServicesSkeleton />;
  }

  return (
    <section className="py-12 bg-gray-50">
      <Container>
        <div className="text-center mb-12">
          <h2 className="text-3xl font-semibold mb-4">
            Våre <span className="text-green-500">tjenester</span>
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Fra Tyrifjordens bredder til åsene rundt Hønefoss – vi skaper varige 
            uterom tilpasset lokale forhold.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          {features.map((feature, index) => (
            <ServiceFeature key={index} {...feature} />
          ))}
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {services.map((service) => (
            <ServiceCard key={service.id} service={service} />
          ))}
        </div>
      </Container>
    </section>
  );
};

const ServicesSkeleton = () => (
  <section className="py-12 bg-gray-50">
    <Container>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div 
            key={i} 
            className="h-[280px] bg-gray-200 rounded-lg animate-pulse"
          />
        ))}
      </div>
    </Container>
  </section>
);

export default Services;```


#### `(sections)\testimonials\Testimonials.tsx`

```tsx
import React, { useState, useEffect, useCallback } from 'react';
import { Container } from '@/components/ui/Container';
import { Star, ArrowLeft, ArrowRight, Quote } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TestimonialProps {
  name: string;
  location: string;
  text: string;
  rating: number;
  projectType: string;
  image?: string;
}

const testimonials: TestimonialProps[] = [
  {
    name: "Anders Hansen",
    location: "Hønefoss",
    text: "Fantastisk arbeid med vår nye hage. Profesjonelt team som leverte over forventning. Spesielt imponert over støttemuren og beplantningen.",
    rating: 5,
    projectType: "Komplett hageprosjekt",
    image: "https://images.unsplash.com/photo-1556157382-97eda2d62296?auto=format&fit=crop&q=80"
  },
  {
    name: "Maria Olsen",
    location: "Røyse",
    text: "Veldig fornøyd med den nye innkjørselen. Belegningssteinen ble lagt perfekt, og løsningen med avrenning fungerer utmerket.",
    rating: 5,
    projectType: "Belegningsstein",
    image: "https://images.unsplash.com/photo-1552058544-f2b08422138a?auto=format&fit=crop&q=80"
  },
  {
    name: "Erik Pedersen",
    location: "Hole",
    text: "Profesjonell og punktlig gjennomføring av prosjektet. God kommunikasjon underveis og resultatet ble akkurat som vi ønsket.",
    rating: 5,
    projectType: "Støttemur og trapp"
  }
];

const Testimonial: React.FC<TestimonialProps> = ({ 
  name, 
  location, 
  text, 
  rating, 
  projectType,
  image
}) => (
  <div 
    className="relative bg-white p-6 rounded-lg shadow-sm transition-all duration-300 hover:shadow-md"
    itemScope 
    itemType="http://schema.org/Review"
  >
    <div className="absolute -top-3 -left-2 text-green-500/10">
      <Quote size={48} />
    </div>
    
    <div className="relative">
      <div className="flex items-center gap-4 mb-4">
        {image ? (
          <img 
            src={image} 
            alt={name}
            className="w-12 h-12 rounded-full object-cover"
          />
        ) : (
          <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
            <span className="text-green-500 text-lg font-medium">
              {name.charAt(0)}
            </span>
          </div>
        )}
        <div>
          <p className="font-medium" itemProp="author">{name}</p>
          <p className="text-sm text-gray-500">{location}</p>
        </div>
      </div>

      <div className="flex gap-1 mb-3">
        {[...Array(5)].map((_, i) => (
          <Star 
            key={i} 
            className={cn(
              "w-4 h-4",
              i < rating ? "text-yellow-400 fill-current" : "text-gray-300"
            )}
          />
        ))}
      </div>

      <p 
        className="text-gray-600 mb-4 leading-relaxed" 
        itemProp="reviewBody"
      >
        {text}
      </p>

      <div className="text-sm text-green-600" itemProp="itemReviewed">
        {projectType}
      </div>
    </div>
  </div>
);

const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const handlePrevious = useCallback(() => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev === 0 ? testimonials.length - 1 : prev - 1));
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating]);

  const handleNext = useCallback(() => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev === testimonials.length - 1 ? 0 : prev + 1));
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating]);

  useEffect(() => {
    const interval = setInterval(handleNext, 6000);
    return () => clearInterval(interval);
  }, [handleNext]);

  const visibleTestimonials = () => {
    const numVisible = window.innerWidth >= 1024 ? 3 : window.innerWidth >= 768 ? 2 : 1;
    const indices = [];
    for (let i = 0; i < numVisible; i++) {
      indices.push((currentIndex + i) % testimonials.length);
    }
    return indices.map(index => testimonials[index]);
  };

  return (
    <section className="py-16 bg-gray-50">
      <Container>
        <div className="text-center mb-12">
          <h2 className="text-2xl sm:text-3xl font-semibold mb-4">
            Hva våre <span className="text-green-500">kunder sier</span>
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Vi er stolte av å ha hjulpet mange fornøyde kunder i Ringerike-regionen 
            med å skape vakre og funksjonelle uterom.
          </p>
        </div>

        <div className="relative">
          <div className="overflow-hidden">
            <div 
              className="flex gap-6 transition-transform duration-500 ease-out"
              style={{ 
                transform: `translateX(-${currentIndex * (100 / visibleTestimonials().length)}%)` 
              }}
            >
              {visibleTestimonials().map((testimonial, index) => (
                <div 
                  key={index} 
                  className="w-full flex-shrink-0"
                  style={{ flex: `0 0 ${100 / visibleTestimonials().length}%` }}
                >
                  <Testimonial {...testimonial} />
                </div>
              ))}
            </div>
          </div>

          <button
            onClick={handlePrevious}
            className="absolute -left-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white shadow-md text-gray-600 hover:text-gray-900 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            aria-label="Forrige tilbakemelding"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>

          <button
            onClick={handleNext}
            className="absolute -right-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white shadow-md text-gray-600 hover:text-gray-900 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            aria-label="Neste tilbakemelding"
          >
            <ArrowRight className="w-5 h-5" />
          </button>

          <div className="flex justify-center gap-2 mt-8">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={cn(
                  "w-2 h-2 rounded-full transition-all duration-300",
                  index === currentIndex 
                    ? "bg-green-500 w-4" 
                    : "bg-gray-300 hover:bg-gray-400"
                )}
                aria-label={`Gå til tilbakemelding ${index + 1}`}
                aria-current={index === currentIndex}
              />
            ))}
          </div>
        </div>
      </Container>
    </section>
  );
};

export default Testimonials;```


#### `common\Button.tsx`

```tsx
import React from 'react';
import { Link } from 'react-router-dom';

interface ButtonProps {
  children: React.ReactNode;
  to?: string;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'primary' | 'secondary' | 'outline';
  className?: string;
  fullWidth?: boolean;
}

const Button: React.FC<ButtonProps> = ({ 
  children, 
  to, 
  onClick, 
  type = 'button',
  variant = 'primary',
  className = '',
  fullWidth = false
}) => {
  const baseStyles = 'inline-flex items-center justify-center px-6 py-2.5 rounded-md transition-all duration-300 font-medium text-sm';
  const variantStyles = {
    primary: 'bg-green-500 text-white hover:bg-green-600 shadow-sm hover:shadow',
    secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200 shadow-sm hover:shadow',
    outline: 'border-2 border-green-500 text-green-500 hover:bg-green-50'
  };

  const widthClass = fullWidth ? 'w-full' : '';
  const buttonClasses = `${baseStyles} ${variantStyles[variant]} ${widthClass} ${className}`;

  if (to) {
    return (
      <Link to={to} className={buttonClasses}>
        {children}
      </Link>
    );
  }

  return (
    <button
      type={type}
      onClick={onClick}
      className={buttonClasses}
    >
      {children}
    </button>
  );
};

export default Button;```


#### `common\Container.tsx`

```tsx
import React from 'react';
import { cn } from '../../lib/utils';

interface ContainerProps {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  as?: keyof JSX.IntrinsicElements;
  padded?: boolean;
}

const Container = React.forwardRef<HTMLDivElement, ContainerProps>(({
  children,
  className,
  size = 'lg',
  as: Component = 'div',
  padded = true
}, ref) => {
  const sizes = {
    sm: 'max-w-3xl',
    md: 'max-w-5xl',
    lg: 'max-w-7xl',
    xl: 'max-w-[90rem]'
  };

  return (
    <Component
      ref={ref}
      className={cn(
        sizes[size],
        'mx-auto',
        padded && 'px-4 sm:px-6 lg:px-8',
        className
      )}
    >
      {children}
    </Component>
  );
});

Container.displayName = 'Container';

export { Container };

export default Container```


#### `common\Hero.tsx`

```tsx
import { Link } from "react-router-dom";
import { HeroProps } from "../../types";
import { ChevronDown } from "lucide-react";

interface EnhancedHeroProps extends HeroProps {
    location?: string;
    yearEstablished?: string;
    actionLink?: string;
    actionText?: string;
}

const Hero: React.FC<EnhancedHeroProps> = ({
    title,
    subtitle,
    // backgroundImage = '/images/site/hero-grass.webp',
    // backgroundImage = '/images/site/hero-grass2.webp',
    // backgroundImage = '/images/site/hero-illustrative.webp',
    // backgroundImage = '/images/site/hero-corten-steel.webp',
    backgroundImage = "/images/site/hero-main.webp",
    location = "Røyse, Hole kommune",
    yearEstablished = "2015",
    actionLink,
    actionText = "Se våre prosjekter",
}) => {
    const scrollToProjects = () => {
        const projectsSection = document.getElementById("recent-projects");
        if (projectsSection) {
            projectsSection.scrollIntoView({ behavior: "smooth" });
        }
    };

    return (
        <section
            className="relative h-[calc(100vh-64px)] min-h-[600px] max-h-[800px] overflow-hidden"
            itemScope
            itemType="http://schema.org/LandscapingService"
        >
            {/* Background image with parallax effect */}
            <div
                className="absolute inset-0 transform scale-105"
                style={{
                    backgroundImage: `url(${backgroundImage})`,
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    backgroundAttachment: "fixed",
                }}
                role="img"
                aria-label="Landskapsprosjekt i Ringerike-området"
            />

            {/* Center square gradient overlay */}
            <div
                className="absolute inset-0"
                style={{
                    background: `
            linear-gradient(
              47deg,
              transparent 12%,
              rgba(0,0,0,0.8) 30%,
              rgba(0,0,0,0.65) 75%,
              transparent 87%
            )
          `,
                }}
                aria-hidden="true"
            />

            {/* Subtle corner darkening */}
            <div
                className="absolute inset-0"
                style={{
                    background:
                        "radial-gradient(circle at center, transparent 80%, rgba(0,0,0,0.9) 100%)",
                }}
                aria-hidden="true"
            />

            {/* Content */}
            <div className="relative h-full flex flex-col justify-center items-center text-center text-white px-4 sm:px-6 z-10">
                <div className="max-w-4xl">
                    {/* Main heading with enhanced typography */}
                    <h1
                        className="text-5xl sm:text-6xl md:text-7xl font-bold mb-8 sm:mb-10 tracking-tight
                       text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-200
                       animate-fadeInUp"
                        itemProp="name"
                    >
                        {title}
                    </h1>

                    {/* Subtitle with improved readability */}
                    {subtitle && (
                        <p
                            className="text-xl sm:text-2xl md:text-3xl max-w-3xl mx-auto leading-relaxed
                         font-light text-white animate-fadeInUp delay-100"
                            itemProp="description"
                        >
                            {subtitle}
                        </p>
                    )}

                    {/* Call to action */}
                    <div className="mt-16 animate-fadeInUp delay-200 flex flex-col items-center gap-8">
                        {actionText &&
                            (actionLink ? (
                                <Link
                                    to={actionLink}
                                    className="group inline-flex flex-col items-center gap-3 transition-transform hover:translate-y-1"
                                    aria-label={actionText}
                                >
                                    <span className="text-base sm:text-lg font-medium text-gray-200">
                                        {actionText}
                                    </span>
                                    <ChevronDown className="w-7 h-7 text-green-400 animate-bounce" />
                                </Link>
                            ) : (
                                <button
                                    onClick={scrollToProjects}
                                    className="group inline-flex flex-col items-center gap-3 transition-transform hover:translate-y-1"
                                    aria-label={actionText}
                                >
                                    <span className="text-base sm:text-lg font-medium text-gray-200">
                                        {actionText}
                                    </span>
                                    <ChevronDown className="w-7 h-7 text-green-400 animate-bounce" />
                                </button>
                            ))}

                        {/* Location badge with enhanced styling */}
                        <div className="animate-fadeIn">
                            <span
                                className="inline-flex items-center px-4 py-1.5 rounded-full bg-green-600/90 text-sm sm:text-base font-medium tracking-wide
                           shadow-lg backdrop-blur-sm transition-transform hover:scale-105"
                                itemProp="areaServed"
                            >
                                <span className="w-1.5 h-1.5 bg-white rounded-full mr-2" />
                                {location}
                            </span>
                        </div>
                    </div>

                    {/* SEO metadata with enhanced attributes */}
                    <meta itemProp="foundingDate" content={yearEstablished} />
                    <meta
                        itemProp="areaServed"
                        content="Ringerike, Hole, Jevnaker, Hønefoss"
                    />
                    <meta itemProp="priceRange" content="NOK" />
                    <meta itemProp="availableLanguage" content="nb-NO" />
                    <meta
                        itemProp="paymentAccepted"
                        content="Cash, Credit Card, Invoice"
                    />
                    <meta itemProp="openingHours" content="Mo-Fr 07:00-16:00" />
                </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent" />

            {/* Image attribution */}
            <div className="sr-only" role="contentinfo">
                Bilder fra våre prosjekter i Ringerike-området
            </div>
        </section>
    );
};

export default Hero;

// CSS animations should be moved to a global CSS file
```


#### `common\ImageGallery.tsx`

```tsx
import React, { useState } from 'react';
import { ImageFile } from '../../utils/imageLoader';
import { X } from 'lucide-react';

interface ImageGalleryProps {
  images: ImageFile[];
  columns?: number;
  gap?: number;
  className?: string;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({ 
  images, 
  columns = 3, 
  gap = 4,
  className = '' 
}) => {
  const [selectedImage, setSelectedImage] = useState<ImageFile | null>(null);

  return (
    <>
      <div 
        className={`grid gap-${gap} ${
          columns === 2 ? 'grid-cols-1 sm:grid-cols-2' :
          columns === 3 ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' :
          columns === 4 ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4' :
          'grid-cols-1'
        } ${className}`}
      >
        {images.map((image, index) => (
          <div 
            key={index}
            className="relative aspect-square overflow-hidden rounded-lg cursor-pointer group"
            onClick={() => setSelectedImage(image)}
          >
            <img
              src={image.path}
              alt={image.metadata?.title || ''}
              className="absolute inset-0 w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
            {image.metadata?.title && (
              <div className="absolute inset-x-0 bottom-0 bg-black/60 text-white p-3 translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                <h4 className="text-sm font-medium">{image.metadata.title}</h4>
                {image.metadata.description && (
                  <p className="text-xs text-gray-200 mt-1">{image.metadata.description}</p>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Modal */}
      {selectedImage && (
        <div 
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <button 
            className="absolute top-4 right-4 text-white p-2 hover:bg-white/10 rounded-full"
            onClick={() => setSelectedImage(null)}
          >
            <X className="w-6 h-6" />
          </button>
          <div className="max-w-4xl w-full">
            <img
              src={selectedImage.path}
              alt={selectedImage.metadata?.title || ''}
              className="w-full h-auto rounded-lg"
            />
            {selectedImage.metadata && (
              <div className="text-white mt-4">
                <h3 className="text-lg font-medium">{selectedImage.metadata.title}</h3>
                {selectedImage.metadata.description && (
                  <p className="text-sm text-gray-300 mt-1">{selectedImage.metadata.description}</p>
                )}
                <div className="flex gap-4 mt-2 text-sm text-gray-400">
                  {selectedImage.metadata.location && (
                    <span>{selectedImage.metadata.location}</span>
                  )}
                  {selectedImage.metadata.date && (
                    <span>{selectedImage.metadata.date}</span>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default ImageGallery;```


#### `common\LocalExpertise.tsx`

```tsx
import React from 'react';
import { Map, Shield, Users } from 'lucide-react';

const LocalExpertise = () => {
  const expertise = [
    {
      icon: <Map className="w-5 h-5" />,
      title: 'Lokalkunnskap',
      description: 'Vi kjenner hver bakketopp og jordsmonn i Ringerike-regionen'
    },
    {
      icon: <Shield className="w-5 h-5" />,
      title: 'Klimatilpasset',
      description: 'Løsninger som tåler både tørre somre og våte høster'
    },
    {
      icon: <Users className="w-5 h-5" />,
      title: 'Lokal forankring',
      description: 'Fast team med base på Røyse'
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      <h3 className="text-lg font-medium mb-4">
        Ekspertise i Ringerike-regionen
      </h3>
      
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        {expertise.map((item, index) => (
          <div 
            key={index}
            className="flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <div className="text-green-500">{item.icon}</div>
            <div>
              <h4 className="font-medium text-sm mb-1">{item.title}</h4>
              <p className="text-sm text-gray-600">{item.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LocalExpertise;```


#### `common\LocalServiceArea.tsx`

```tsx
import ServiceAreaList from './ServiceAreaList';

const LocalServiceArea = () => {
  return <ServiceAreaList />;
};

export default LocalServiceArea```


#### `common\Logo.tsx`

```tsx
import React from 'react';

interface LogoProps {
  className?: string;
  color?: string;
}

const Logo: React.FC<LogoProps> = ({ className = 'w-8 h-8', color = '#1e9545' }) => {
  return (
    <svg 
      viewBox="0 0 476.43 554.74" 
      className={className}
      aria-label="Ringerike Landskap logo"
    >
      <path
        fill={color}
        fillRule="evenodd"
        d="M182.5,274.98l46.8,81.27-23.4,46.76,42.34,11.13-17.83,42.3,53.49-57.89-52.37-5.57,113.66-214.85,71.32,131.66V90.6c0-23.59-19.15-42.72-42.77-42.72H102.69c-23.62,0-42.77,19.12-42.77,42.72v384.27c0,.52.02,1.04.04,1.56l122.54-201.45Z"
      />
    </svg>
  );
};

export default Logo```


#### `common\SeasonalCTA.tsx`

```tsx
import React from 'react';
import { Calendar, Snowflake } from 'lucide-react';

const SeasonalCTA = () => {
  const getCurrentSeason = () => {
    const month = new Date().getMonth();
    if (month >= 2 && month <= 4) return 'vår';
    if (month >= 5 && month <= 7) return 'sommer';
    if (month >= 8 && month <= 10) return 'høst';
    return 'vinter';
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-4 sm:p-5">
      <div className="flex items-center gap-2 mb-4">
        <Snowflake className="w-5 h-5 text-blue-500" />
        <div className="flex items-center gap-1.5 text-sm">
          <Calendar className="w-4 h-4 text-gray-500" />
          <span>Sesong: {getCurrentSeason()}</span>
        </div>
      </div>

      <h3 className="text-lg font-medium mb-3">
        Planlegg for Ringerike-våren
      </h3>
      
      <p className="text-sm text-gray-600 mb-4">
        Vinteren er perfekt for å planlegge neste sesongs hageprosjekter. 
        Vi har inngående kjennskap til lokale forhold i Ringerike.
      </p>

      <div className="space-y-3 mb-4">
        <h4 className="text-sm font-medium">Aktuelle tjenester:</h4>
        <ul className="space-y-2">
          <li className="flex items-center gap-2 text-sm text-gray-600">
            <span className="w-1.5 h-1.5 bg-green-500 rounded-full" />
            Lokaltilpasset planlegging
          </li>
          <li className="flex items-center gap-2 text-sm text-gray-600">
            <span className="w-1.5 h-1.5 bg-green-500 rounded-full" />
            Terrengvurdering
          </li>
          <li className="flex items-center gap-2 text-sm text-gray-600">
            <span className="w-1.5 h-1.5 bg-green-500 rounded-full" />
            Klimatilpasset design
          </li>
        </ul>
      </div>

      <button className="w-full bg-green-500 text-white text-sm px-4 py-2.5 rounded-md hover:bg-green-600 transition-colors">
        Book gratis befaring i Ringerike
      </button>
    </div>
  );
};

export default SeasonalCTA;```


#### `common\ServiceAreaList.tsx`

```tsx
import { MapPin } from 'lucide-react';
import { SERVICE_AREAS } from '../../lib/constants';
import { ServiceArea } from '../../types';

interface ServiceAreaListProps {
  variant?: 'compact' | 'default';
  title?: string;
  showDescription?: boolean;
  className?: string;
  footerText?: string;
  areas?: ServiceArea[];
}

/**
 * A reusable component for displaying service areas
 * Can be used in different contexts with different styling variants
 */
const ServiceAreaList = ({
  variant = 'default',
  title = 'Vårt dekningsområde',
  showDescription = false,
  className = '',
  footerText = 'Med base på Røyse betjener vi hele Ringerike-regionen med lokalkunnskap og personlig service',
  areas = SERVICE_AREAS
}: ServiceAreaListProps) => {
  const isCompact = variant === 'compact';
  
  return (
    <div 
      className={`bg-white rounded-lg ${isCompact ? 'p-4' : 'shadow-sm p-4 sm:p-5 h-full'} ${className}`}
      itemScope 
      itemType="http://schema.org/LocalBusiness"
      role="region"
      aria-label="Service area information"
    >
      <div className="flex items-center gap-2 mb-4">
        <MapPin className={`${isCompact ? 'w-4 h-4' : 'w-5 h-5'} text-green-500`} />
        <h3 className={`${isCompact ? 'text-base' : 'text-lg'} font-medium tracking-tight`}>
          {title}
        </h3>
      </div>
      
      <div className="space-y-2">
        {areas.map((area) => (
          <div 
            key={area.city}
            className={`flex items-center justify-between p-2.5 rounded-md transition-colors duration-200 ${
              area.isBase 
                ? 'bg-green-50 border border-green-100' 
                : 'hover:bg-gray-50 hover:shadow-sm'
            }`}
          >
            <div className="flex items-center gap-2">
              {area.isBase && (
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
              )}
              <span className={`${isCompact ? 'text-xs' : 'text-sm'} ${area.isBase ? 'font-medium' : ''}`}>
                {area.city}
              </span>
            </div>
            <div className="flex flex-col items-end">
              <span className={`${isCompact ? 'text-xs' : 'text-sm'} text-gray-600`}>
                {area.distance}
              </span>
              {showDescription && area.description && (
                <span className="text-xs text-gray-500 mt-0.5">{area.description}</span>
              )}
            </div>
          </div>
        ))}
      </div>

      {footerText && (
        <p className="mt-4 text-sm text-gray-600 leading-relaxed">
          {footerText}
        </p>
      )}
    </div>
  );
};

export default ServiceAreaList; ```


#### `common\WeatherAdaptedServices.tsx`

```tsx
import React from 'react';
import { Cloud, Sun, CloudRain } from 'lucide-react';

const WeatherAdaptedServices = () => {
  const seasons = [
    {
      name: 'Vår',
      icon: <Cloud className="w-6 h-6" />,
      services: [
        'Vårklargjøring tilpasset Ringerikes klima',
        'Drenering for lokalt jordsmonn',
        'Tidlig plantesesong'
      ]
    },
    {
      name: 'Sommer',
      icon: <Sun className="w-6 h-6" />,
      services: [
        'Vanningsløsninger for tørre perioder',
        'Skyggeplanlegging med lokale arter',
        'Vedlikehold av grøntarealer'
      ]
    },
    {
      name: 'Høst',
      icon: <CloudRain className="w-6 h-6" />,
      services: [
        'Høstklargjøring for Ringerikes vinter',
        'Drenering og fallsikring',
        'Beskjæring av lokale arter'
      ]
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h3 className="text-xl font-semibold mb-6">
        Tilpasset Ringerikes årstider
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {seasons.map((season) => (
          <div 
            key={season.name}
            className="p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
          >
            <div className="flex items-center gap-2 mb-3">
              <div className="text-green-500">{season.icon}</div>
              <h4 className="font-medium">{season.name}</h4>
            </div>
            <ul className="space-y-2 text-sm text-gray-600">
              {season.services.map((service, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-green-500 rounded-full mt-1.5" />
                  {service}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
};

export default WeatherAdaptedServices;```


#### `contact\ContactForm.tsx`

```tsx
import React, { useState } from 'react';

interface FormData {
  name: string;
  email: string;
  phone: string;
  address: string;
  description: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  description?: string;
}

const ContactForm = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    address: '',
    description: ''
  });
  
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [errors, setErrors] = useState<FormErrors>({});

  const validateField = (name: string, value: string): string => {
    switch (name) {
      case 'name':
        return value.trim().length < 2 ? 'Navn må være minst 2 tegn' : '';
      case 'email':
        return !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value) ? 'Ugyldig e-postadresse' : '';
      case 'phone':
        return !/^(\+47)?[2-9]\d{7}$/.test(value.replace(/\s/g, '')) ? 'Ugyldig telefonnummer (8 siffer)' : '';
      case 'address':
        return value.trim().length < 5 ? 'Vennligst skriv inn full adresse' : '';
      case 'description':
        return value.trim().length < 10 ? 'Beskrivelsen må være minst 10 tegn' : '';
      default:
        return '';
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Validate on change if field has been touched
    if (touched[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: validateField(name, value)
      }));
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setTouched(prev => ({ ...prev, [name]: true }));
    setErrors(prev => ({
      ...prev,
      [name]: validateField(name, value)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate all fields
    const newErrors: FormErrors = {};
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key as keyof FormData]);
      if (error) {
        newErrors[key as keyof FormErrors] = error;
      }
    });
    
    // Mark all fields as touched
    setTouched(Object.keys(formData).reduce((acc, key) => ({ ...acc, [key]: true }), {}));
    setErrors(newErrors);

    // If no errors, submit the form
    if (Object.keys(newErrors).length === 0) {
      console.log('Form submitted:', formData);
    }
  };

  const getInputClassName = (fieldName: keyof FormData) => {
    const baseClasses = "w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent";
    return `${baseClasses} ${
      touched[fieldName] && errors[fieldName]
        ? "border-red-500 text-red-900 placeholder-red-300"
        : "border-gray-300"
    }`;
  };

  return (
    <div className="max-w-2xl mx-auto p-4 sm:p-6 lg:p-8">
      <h2 className="text-2xl sm:text-3xl font-semibold mb-2">
        Kom i kontakt <span className="text-green-500">med oss</span>
      </h2>
      <p className="text-gray-600 mb-8">
        Har du spørsmål, ønsker en befaring eller trenger hjelp? Ikke nøl med å kontakte oss!
      </p>
      <form onSubmit={handleSubmit} className="space-y-4" noValidate>
        <div className="space-y-1">
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder="Navn *"
            className={getInputClassName('name')}
            required
          />
          {touched.name && errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name}</p>
          )}
        </div>
        
        <div className="space-y-1">
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder="E-post *"
            className={getInputClassName('email')}
            required
          />
          {touched.email && errors.email && (
            <p className="text-red-500 text-sm mt-1">{errors.email}</p>
          )}
        </div>
        
        <div className="space-y-1">
          <input
            type="tel"
            name="phone"
            value={formData.phone}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder="Telefonnummer *"
            className={getInputClassName('phone')}
            required
          />
          {touched.phone && errors.phone && (
            <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
          )}
        </div>
        
        <div className="space-y-1">
          <input
            type="text"
            name="address"
            value={formData.address}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder="Adresse *"
            className={getInputClassName('address')}
            required
          />
          {touched.address && errors.address && (
            <p className="text-red-500 text-sm mt-1">{errors.address}</p>
          )}
        </div>
        
        <div className="space-y-1">
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder="Beskriv prosjektet ditt *"
            rows={4}
            className={getInputClassName('description')}
            required
          />
          {touched.description && errors.description && (
            <p className="text-red-500 text-sm mt-1">{errors.description}</p>
          )}
        </div>
        
        <button
          type="submit"
          className="w-full sm:w-auto bg-green-500 text-white px-8 py-3 rounded-md hover:bg-green-600 transition-colors"
        >
          Send melding
        </button>
      </form>
    </div>
  );
};

export default ContactForm;```


#### `ContactForm.tsx`

```tsx
import React from 'react';

const ContactForm = () => {
  return (
    <div className="max-w-2xl mx-auto p-4 sm:p-6 lg:p-8">
      <h2 className="text-2xl sm:text-3xl font-semibold mb-2">
        Kom i kontakt <span className="text-green-500">med oss</span>
      </h2>
      <p className="text-gray-600 mb-8">
        Har du spørsmål, ønsker en befaring eller trenger hjelp? Ikke nøl med å kontakte oss!
      </p>
      <form className="space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <input
            type="text"
            placeholder="Navn"
            className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
          <input
            type="text"
            placeholder="Budsjett"
            className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>
        <input
          type="email"
          placeholder="E-post"
          className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
        />
        <input
          type="tel"
          placeholder="Telefonnummer"
          className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
        />
        <input
          type="text"
          placeholder="Adresse"
          className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
        />
        <textarea
          placeholder="Beskriv prosjektet ditt"
          rows={4}
          className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
        />
        <button
          type="submit"
          className="w-full sm:w-auto bg-green-500 text-white px-8 py-3 rounded-md hover:bg-green-600 transition-colors"
        >
          Send melding
        </button>
      </form>
    </div>
  );
};

export default ContactForm;```


#### `index.ts`

```typescript
// UI Components
export * from './ui/Button';
export * from './ui/Container';
export * from './ui/Hero';
export * from './ui/Logo';

// Layout Components
export * from './layout/Header';
export * from './layout/Footer';
export * from './layout/Meta';

// Feature Components
export * from './features/projects';
export * from './features/services';
export * from './features/testimonials';```


#### `layout\Footer.tsx`

```tsx
import React from "react";
import { Link } from "react-router-dom";
import { MapPin, Phone, Mail, Clock, Facebook } from "lucide-react";
import { Logo } from "@/components/ui/Logo";

const Footer = () => {
    const currentYear = new Date().getFullYear();

    return (
        <footer className="bg-gray-900 text-white pt-12 pb-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                    {/* Company info */}
                    <div>
                        <div className="flex items-center gap-2 mb-4">
                            <Logo variant="full" className="text-white" />
                        </div>
                        <p className="text-gray-400 text-sm mb-4">
                            Vi skaper varige uterom tilpasset Ringerikes unike
                            terreng og klima. Med base på Røyse betjener vi hele
                            regionen med kvalitetsarbeid og personlig service.
                        </p>
                        <div className="flex items-center gap-2">
                            <Facebook className="w-5 h-5 text-gray-400" />
                            <a
                                href="https://www.facebook.com/ringerikelandskap"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-gray-400 hover:text-white text-sm"
                            >
                                Følg oss på Facebook
                            </a>
                        </div>
                    </div>

                    {/* Contact info */}
                    <div>
                        <h3 className="text-lg font-semibold mb-4">
                            Kontakt oss
                        </h3>
                        <ul className="space-y-3">
                            <li className="flex items-start gap-3">
                                <MapPin className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                                <div>
                                    <p>Birchs vei 7</p>
                                    <p>3530 Røyse</p>
                                </div>
                            </li>
                            <li className="flex items-center gap-3">
                                <Phone className="w-5 h-5 text-green-500 flex-shrink-0" />
                                <a
                                    href="tel:+4790214153"
                                    className="hover:text-green-400"
                                >
                                    +47 902 14 153
                                </a>
                            </li>
                            <li className="flex items-center gap-3">
                                <Mail className="w-5 h-5 text-green-500 flex-shrink-0" />
                                <a
                                    href="mailto:<EMAIL>"
                                    className="hover:text-green-400"
                                >
                                    <EMAIL>
                                </a>
                            </li>
                        </ul>
                    </div>

                    {/* Opening hours */}
                    <div>
                        <h3 className="text-lg font-semibold mb-4">
                            Åpningstider
                        </h3>
                        <ul className="space-y-3">
                            <li className="flex items-start gap-3">
                                <Clock className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                                <div>
                                    <p className="font-medium">
                                        Mandag - Fredag:
                                    </p>
                                    <p>07:00-16:00</p>
                                </div>
                            </li>
                            <li className="flex items-start gap-3">
                                <Clock className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                                <div>
                                    <p className="font-medium">
                                        Lørdag - Søndag:
                                    </p>
                                    <p>Stengt</p>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                {/* Quick links */}
                <div className="border-t border-gray-800 pt-8 pb-4">
                    <div className="flex flex-col md:flex-row justify-between items-center">
                        <div className="flex flex-wrap justify-center md:justify-start gap-6 mb-4 md:mb-0">
                            <Link
                                to="/"
                                className="text-gray-400 hover:text-white text-sm"
                            >
                                Hjem
                            </Link>
                            <Link
                                to="/hvem-er-vi"
                                className="text-gray-400 hover:text-white text-sm"
                            >
                                Hvem er vi
                            </Link>
                            <Link
                                to="/hva-vi-gjor"
                                className="text-gray-400 hover:text-white text-sm"
                            >
                                Hva vi gjør
                            </Link>
                            <Link
                                to="/prosjekter"
                                className="text-gray-400 hover:text-white text-sm"
                            >
                                Prosjekter
                            </Link>
                            <Link
                                to="/kontakt"
                                className="text-gray-400 hover:text-white text-sm"
                            >
                                Kontakt oss
                            </Link>
                        </div>
                        <div className="text-gray-500 text-sm">
                            © {currentYear} Ringerike Landskap AS. Org.nr: 123
                            456 789
                        </div>
                    </div>
                </div>

                {/* Privacy links */}
                <div className="flex justify-center md:justify-end mt-4 gap-4">
                    <Link
                        to="/personvern"
                        className="text-gray-500 hover:text-gray-400 text-xs"
                    >
                        Personvern
                    </Link>
                    <Link
                        to="/cookies"
                        className="text-gray-500 hover:text-gray-400 text-xs"
                    >
                        Cookies
                    </Link>
                </div>
            </div>
        </footer>
    );
};

export default Footer;```


#### `layout\Hero.tsx`

```tsx
import React from 'react';
import { HeroProps } from '@/lib/types';
import { Container } from '@/components/ui/Container';
import { Button } from '@/components/ui/Button';
import { ArrowRight } from 'lucide-react';

const Hero: React.FC<HeroProps> = ({ 
  title = "Anleggsgartner i Ringerike",
  subtitle = "Med base på Røyse skaper vi varige uterom tilpasset Ringerikes unike terreng og klima.",
  backgroundImage = "/images/categorized/hero-main.webp"
}) => {
  return (
    <section 
      className="relative min-h-[480px] sm:min-h-[580px] flex items-center justify-center text-white overflow-hidden"
      style={{
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      }}
      role="banner"
      aria-label="Hero section"
    >
      {/* Rest of the component remains the same */}
    </section>
  );
};

export default Hero;```


#### `layout\Layout.tsx`

```tsx
import React from "react";
import Navbar from "./Navbar";
import Footer from "./Footer";

interface LayoutProps {
    children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
    return (
        <div className="flex flex-col min-h-screen">
            <Navbar />
            <main className="flex-grow">{children}</main>
            <Footer />
        </div>
    );
};

export default Layout;
```


#### `layout\Meta.tsx`

```tsx
import React from 'react';
import { Helmet } from 'react-helmet';
import { SITE_CONFIG, CONTACT_INFO, LOCAL_INFO } from '@/lib/constants';

interface MetaProps {
  title?: string;
  description?: string;
  image?: string;
  type?: 'website' | 'article';
  noindex?: boolean;
  schema?: Record<string, unknown>;
  keywords?: string[];
  location?: {
    city: string;
    region?: string;
  };
}

export const Meta: React.FC<MetaProps> = ({
  title,
  description = SITE_CONFIG.description,
  image = SITE_CONFIG.ogImage,
  type = 'website',
  noindex = false,
  schema,
  keywords = SITE_CONFIG.keywords,
  location
}) => {
  const fullTitle = title 
    ? `${title} | ${SITE_CONFIG.name}`
    : location
    ? `Anleggsgartner i ${location.city} | ${SITE_CONFIG.name}`
    : SITE_CONFIG.name;

  const localDescription = location
    ? `Din lokale anleggsgartner i ${location.city}. Vi har inngående kjennskap til lokale forhold og leverer skreddersydde løsninger for ditt uterom.`
    : description;

  const baseSchema = {
    "@context": "https://schema.org",
    "@type": "LandscapingBusiness",
    "name": SITE_CONFIG.name,
    "image": SITE_CONFIG.ogImage,
    "description": localDescription,
    "@id": SITE_CONFIG.url,
    "url": SITE_CONFIG.url,
    "telephone": SITE_CONFIG.phone,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": CONTACT_INFO.address.street,
      "addressLocality": CONTACT_INFO.address.city,
      "postalCode": CONTACT_INFO.address.postalCode,
      "addressRegion": CONTACT_INFO.address.county,
      "addressCountry": "NO"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": 60.0558,
      "longitude": 10.2558
    },
    "areaServed": {
      "@type": "GeoCircle",
      "geoMidpoint": {
        "@type": "GeoCoordinates",
        "latitude": 60.0558,
        "longitude": 10.2558
      },
      "geoRadius": "25000"
    },
    "openingHoursSpecification": {
      "@type": "OpeningHoursSpecification",
      "dayOfWeek": [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday"
      ],
      "opens": "07:00",
      "closes": "16:00"
    },
    "priceRange": "$$",
    "sameAs": [
      CONTACT_INFO.social.facebook
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Anleggsgartnertjenester i Hole og Ringerike",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Belegningsstein",
            "description": "Profesjonell legging av belegningsstein tilpasset lokale forhold"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Støttemurer",
            "description": "Bygging av støttemurer tilpasset terrenget i Hole og Ringerike"
          }
        }
      ]
    },
    "knowsAbout": [
      "Landskapsarkitektur",
      "Hagedesign",
      "Belegningsstein",
      "Støttemurer",
      "Norsk klima",
      "Lokale jordforhold",
      "Stedegen beplantning"
    ],
    "knowsLanguage": [
      {
        "@type": "Language",
        "name": "Norwegian",
        "alternateName": "nb"
      }
    ],
    "serviceArea": {
      "@type": "GeoCircle",
      "geoMidpoint": {
        "@type": "GeoCoordinates",
        "latitude": 60.0558,
        "longitude": 10.2558
      },
      "geoRadius": "25000"
    }
  };

  const localKeywords = location
    ? [...keywords, `anleggsgartner ${location.city}`, `hagearbeid ${location.city}`]
    : keywords;

  return (
    <Helmet>
      <title>{fullTitle}</title>
      <meta name="description" content={localDescription} />
      <meta name="keywords" content={localKeywords.join(', ')} />
      
      {/* Open Graph */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={localDescription} />
      <meta property="og:image" content={image} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content={SITE_CONFIG.name} />
      <meta property="og:locale" content={SITE_CONFIG.locale} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      
      {/* Local Business */}
      <meta name="geo.region" content="NO-VIK" />
      <meta name="geo.placename" content="Hole" />
      <meta name="geo.position" content="60.0558;10.2558" />
      <meta name="ICBM" content="60.0558, 10.2558" />
      
      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={localDescription} />
      <meta name="twitter:image" content={image} />
      
      {/* Indexing */}
      {noindex ? (
        <meta name="robots" content="noindex,nofollow" />
      ) : (
        <meta name="robots" content="index,follow,max-image-preview:large" />
      )}
      
      {/* Canonical URL */}
      <link rel="canonical" href={`${SITE_CONFIG.url}${window.location.pathname}`} />
      
      {/* Language */}
      <html lang={SITE_CONFIG.locale.split('_')[0]} />

      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(schema || baseSchema)}
      </script>

      {/* Resource Hints */}
      <link rel="preconnect" href="https://images.unsplash.com" />
      <link rel="dns-prefetch" href="https://images.unsplash.com" />
      
      {/* Security Headers */}
      <meta http-equiv="Content-Security-Policy" content="default-src 'self'; img-src 'self' https://images.unsplash.com data:; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; connect-src 'self';" />
      <meta http-equiv="X-Content-Type-Options" content="nosniff" />
      <meta http-equiv="X-Frame-Options" content="SAMEORIGIN" />
      <meta http-equiv="Permissions-Policy" content="geolocation=(), microphone=(), camera=()" />
      <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
    </Helmet>
  );
};```


#### `layout\Navbar.tsx`

```tsx
import React, { useState } from "react";
import { Link } from "react-router-dom";
import { Menu, X } from "lucide-react";
import { Logo } from "@/components/ui/Logo";

const Navbar = () => {
    const [isMenuOpen, setIsMenuOpen] = useState(false);

    return (
        <nav className="relative bg-white shadow-sm">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between h-16">
                    <Link to="/" className="flex items-center">
                        <Logo variant="full" className="text-gray-900" />
                    </Link>

                    {/* Mobile menu button */}
                    <div className="flex items-center sm:hidden">
                        <button
                            onClick={() => setIsMenuOpen(!isMenuOpen)}
                            className="text-gray-600 hover:text-gray-900"
                        >
                            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
                        </button>
                    </div>

                    {/* Desktop menu */}
                    <div className="hidden sm:flex sm:items-center sm:gap-8">
                        <Link to="/" className="hover:text-green-600">
                            Hjem
                        </Link>
                        <Link to="/hvem-er-vi" className="hover:text-green-600">
                            Hvem er vi
                        </Link>
                        <Link
                            to="/hva-vi-gjor"
                            className="hover:text-green-600"
                        >
                            Hva vi gjør
                        </Link>
                        <Link to="/prosjekter" className="hover:text-green-600">
                            Prosjekter
                        </Link>
                        <Link
                            to="/kontakt"
                            className="bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600"
                        >
                            Kontakt oss
                        </Link>
                    </div>
                </div>
            </div>

            {/* Mobile menu */}
            <div className={`sm:hidden ${isMenuOpen ? "block" : "hidden"}`}>
                <div className="px-2 pt-2 pb-3 space-y-1 bg-white shadow-lg">
                    <Link
                        to="/"
                        className="block px-3 py-2 rounded-md hover:bg-green-50 hover:text-green-600"
                        onClick={() => setIsMenuOpen(false)}
                    >
                        Hjem
                    </Link>
                    <Link
                        to="/hvem-er-vi"
                        className="block px-3 py-2 rounded-md hover:bg-green-50 hover:text-green-600"
                        onClick={() => setIsMenuOpen(false)}
                    >
                        Hvem er vi
                    </Link>
                    <Link
                        to="/hva-vi-gjor"
                        className="block px-3 py-2 rounded-md hover:bg-green-50 hover:text-green-600"
                        onClick={() => setIsMenuOpen(false)}
                    >
                        Hva vi gjør
                    </Link>
                    <Link
                        to="/prosjekter"
                        className="block px-3 py-2 rounded-md hover:bg-green-50 hover:text-green-600"
                        onClick={() => setIsMenuOpen(false)}
                    >
                        Prosjekter
                    </Link>
                    <Link
                        to="/kontakt"
                        className="block px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                        onClick={() => setIsMenuOpen(false)}
                    >
                        Kontakt oss
                    </Link>
                </div>
            </div>
        </nav>
    );
};

export default Navbar;```


#### `local\SeasonalGuide.tsx`

```tsx
import React from 'react';
import { Cloud, Sun, CloudRain } from 'lucide-react';

const seasons = [
  {
    name: 'Vår',
    icon: <Cloud className="w-6 h-6" />,
    services: [
      'Vårklargjøring tilpasset Ringerikes klima',
      'Drenering for lokalt jordsmonn',
      'Tidlig plantesesong'
    ]
  },
  {
    name: 'Sommer',
    icon: <Sun className="w-6 h-6" />,
    services: [
      'Vanningsløsninger for tørre perioder',
      'Skyggeplanlegging med lokale arter',
      'Vedlikehold av grøntarealer'
    ]
  },
  {
    name: 'Høst',
    icon: <CloudRain className="w-6 h-6" />,
    services: [
      'Høstklargjøring for Ringerikes vinter',
      'Drenering og fallsikring',
      'Beskjæring av lokale arter'
    ]
  }
];

const SeasonalGuide = () => {
  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h3 className="text-xl font-semibold mb-6">
        Tilpasset Ringerikes årstider
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {seasons.map((season) => (
          <div 
            key={season.name}
            className="p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
          >
            <div className="flex items-center gap-2 mb-3">
              <div className="text-green-500">{season.icon}</div>
              <h4 className="font-medium">{season.name}</h4>
            </div>
            <ul className="space-y-2 text-sm text-gray-600">
              {season.services.map((service, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-green-500 rounded-full mt-1.5" />
                  {service}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SeasonalGuide;```


#### `local\ServiceAreaMap.tsx`

```tsx
import ServiceAreaList from '../common/ServiceAreaList';

const ServiceAreaMap = () => {
  return (
    <ServiceAreaList 
      title="DekningsomrÃ¥de"
      showDescription={true}
      className="shadow-lg p-6"
      footerText="Vi tar oppdrag i hele Ringerike-regionen og tilpasser oss dine behov."
    />
  );
};

export default ServiceAreaMap;```


#### `local\WeatherNotice.tsx`

```tsx
import React from 'react';
import { Cloud, Sun, CloudRain } from 'lucide-react';

const WeatherNotice = () => {
  const getCurrentSeason = () => {
    const month = new Date().getMonth();
    if (month >= 2 && month <= 4) return 'vår';
    if (month >= 5 && month <= 7) return 'sommer';
    return 'høst';
  };

  const getWeatherInfo = () => {
    const season = getCurrentSeason();
    switch (season) {
      case 'vår':
        return {
          icon: <Cloud className="w-8 h-8 text-blue-500" />,
          title: 'Vårklargjøring i Ringerike',
          description: 'Nå er det perfekt tid for planlegging av sommerens hageprosjekter. Vi kjenner de lokale forholdene og vet hva som skal til.'
        };
      case 'sommer':
        return {
          icon: <Sun className="w-8 h-8 text-yellow-500" />,
          title: 'Sommertid i Ringerike',
          description: 'Perfekt tid for etablering av nye uteområder. Vi har løsninger som tåler både sol og regn.'
        };
      default:
        return {
          icon: <CloudRain className="w-8 h-8 text-gray-500" />,
          title: 'Høstsesongen er her',
          description: 'La oss hjelpe deg med å forberede hagen for vinteren. Vi vet hva som kreves i Ringerike-klimaet.'
        };
    }
  };

  const weather = getWeatherInfo();

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center gap-4 mb-4">
        {weather.icon}
        <h3 className="text-xl font-semibold">{weather.title}</h3>
      </div>
      <p className="text-gray-600">{weather.description}</p>
    </div>
  );
};

export default WeatherNotice;```


#### `Navbar.tsx`

```tsx
import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Leaf, Menu, X } from 'lucide-react';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className="relative bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <Link to="/" className="flex items-center gap-2">
            <Leaf className="w-8 h-8 text-green-600" />
            <span className="text-xl font-semibold hidden sm:block">RINGERIKE LANDSKAP</span>
          </Link>
          
          {/* Mobile menu button */}
          <div className="flex items-center sm:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-600 hover:text-gray-900"
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>

          {/* Desktop menu */}
          <div className="hidden sm:flex sm:items-center sm:gap-8">
            <Link to="/" className="hover:text-green-600">Hjem</Link>
            <Link to="/hvem-er-vi" className="hover:text-green-600">Hvem er vi</Link>
            <Link to="/hva-vi-gjor" className="hover:text-green-600">Hva vi gjør</Link>
            <Link to="/kontakt" className="bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600">
              Kontakt oss
            </Link>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className={`sm:hidden ${isMenuOpen ? 'block' : 'hidden'}`}>
        <div className="px-2 pt-2 pb-3 space-y-1 bg-white shadow-lg">
          <Link 
            to="/" 
            className="block px-3 py-2 rounded-md hover:bg-green-50 hover:text-green-600"
            onClick={() => setIsMenuOpen(false)}
          >
            Hjem
          </Link>
          <Link 
            to="/hvem-er-vi" 
            className="block px-3 py-2 rounded-md hover:bg-green-50 hover:text-green-600"
            onClick={() => setIsMenuOpen(false)}
          >
            Hvem er vi
          </Link>
          <Link 
            to="/hva-vi-gjor" 
            className="block px-3 py-2 rounded-md hover:bg-green-50 hover:text-green-600"
            onClick={() => setIsMenuOpen(false)}
          >
            Hva vi gjør
          </Link>
          <Link 
            to="/kontakt" 
            className="block px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
            onClick={() => setIsMenuOpen(false)}
          >
            Kontakt oss
          </Link>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;```


#### `projects\ProjectCard.tsx`

```tsx
import React, { useState } from 'react';
import { MapPin, Tag, ChevronRight, Star, Ruler, Clock, Leaf, Droplets, Mountain } from 'lucide-react';
import { ProjectCardProps } from '../../types';
import { Link } from 'react-router-dom';

const ProjectCard: React.FC<ProjectCardProps> = ({ 
  project, 
  variant = 'default',
  showTestimonial = false 
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const cardVariants = {
    default: {
      width: '100%',
      height: variant === 'compact' ? '320px' : '400px',
    },
    featured: {
      width: '100%',
      height: '480px',
    },
    compact: {
      width: '100%',
      height: '320px',
    }
  };

  // Personalized location descriptions that feel natural and local
  const getLocationContext = (location: string) => {
    const localContext = {
      'Røyse': {
        prefix: 'Her på',
        description: 'med utsikt over Tyrifjorden'
      },
      'Hole': {
        prefix: 'I hjertet av',
        description: 'mellom åser og vann'
      },
      'Vik': {
        prefix: 'Ved',
        description: 'langs Steinsfjorden'
      },
      'Hønefoss': {
        prefix: 'I',
        description: 'ved fossens kraft'
      },
      'Sundvollen': {
        prefix: 'Ved foten av',
        description: 'med Krokkleiva i ryggen'
      },
      'Jevnaker': {
        prefix: 'På',
        description: 'mot Randsfjorden'
      }
    };

    const context = localContext[location as keyof typeof localContext] || { prefix: 'I', description: '' };
    return {
      short: `${context.prefix} ${location}`,
      full: context.description
    };
  };

  // Enhance features with local terrain context
  const getTerrainFeatures = (specs: typeof project.specifications) => {
    const features = [];
    
    if (specs.materials.some(m => m.includes('naturstein'))) {
      features.push('Tilpasset terrenget');
    }
    if (specs.features.some(f => f.includes('drenering'))) {
      features.push('Lokal overvannshåndtering');
    }
    if (specs.features.some(f => f.includes('vanning'))) {
      features.push('Klimasmart løsning');
    }
    
    return features;
  };

  const locationContext = getLocationContext(project.location);
  const terrainFeatures = getTerrainFeatures(project.specifications);

  return (
    <article 
      className={`
        group relative rounded-xl overflow-hidden bg-white 
        shadow-lg hover:shadow-xl transition-all duration-500
        ${variant === 'featured' ? 'lg:col-span-2 lg:row-span-2' : ''}
      `}
      style={cardVariants[variant]}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      itemScope 
      itemType="http://schema.org/LandscapeProject"
    >
      {/* Image Container with Local Context */}
      <div className="relative w-full h-full">
        <div 
          className="absolute inset-0 bg-cover bg-center transition-transform duration-700 group-hover:scale-105"
          style={{ backgroundImage: `url(${project.image})` }}
          role="img"
          aria-label={`${project.title} - ${locationContext.short}`}
        />
        
        {/* Enhanced Gradient Overlay */}
        <div 
          className={`
            absolute inset-0 transition-opacity duration-500
            bg-gradient-to-t from-black/90 via-black/40 to-transparent
            ${isHovered ? 'opacity-95' : 'opacity-85'}
          `}
        />

        {/* Content Container */}
        <div className="absolute inset-0 p-6 flex flex-col justify-end">
          {/* Local Context Badge */}
          <div className="flex items-center gap-2 mb-4">
            <span 
              className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full 
                         bg-green-600/90 text-white text-sm font-medium
                         backdrop-blur-sm shadow-lg transform transition-all
                         group-hover:translate-y-0 group-hover:opacity-100"
              itemProp="areaServed"
            >
              <MapPin className="w-4 h-4" />
              {locationContext.short}
            </span>
            {locationContext.full && (
              <span className="text-xs text-gray-300 italic">
                {locationContext.full}
              </span>
            )}
          </div>

          {/* Project Content */}
          <div className="transform transition-all duration-300 group-hover:translate-y-0">
            <h3 
              className="text-xl sm:text-2xl font-bold text-white mb-2"
              itemProp="name"
            >
              {project.title}
            </h3>
            
            <p 
              className="text-gray-200 text-sm mb-4"
              itemProp="description"
            >
              {project.description}
            </p>

            {/* Project Details */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="flex items-center gap-2 text-gray-300">
                <Ruler className="w-4 h-4" />
                <span className="text-sm">{project.specifications.size}</span>
              </div>
              <div className="flex items-center gap-2 text-gray-300">
                <Clock className="w-4 h-4" />
                <span className="text-sm">{project.specifications.duration}</span>
              </div>
            </div>

            {/* Terrain Features */}
            {terrainFeatures.length > 0 && (
              <div className="flex flex-wrap gap-3 mb-4">
                {terrainFeatures.map((feature, index) => (
                  <span 
                    key={index}
                    className="inline-flex items-center gap-1.5 text-xs text-green-300"
                  >
                    {feature.includes('terreng') ? (
                      <Mountain className="w-3 h-3" />
                    ) : feature.includes('vann') ? (
                      <Droplets className="w-3 h-3" />
                    ) : (
                      <Leaf className="w-3 h-3" />
                    )}
                    {feature}
                  </span>
                ))}
              </div>
            )}

            {/* Project Tags */}
            <div className="flex flex-wrap gap-2 mb-4">
              {project.tags.slice(0, 3).map((tag, index) => (
                <span 
                  key={index}
                  className="inline-flex items-center gap-1 px-2 py-1 
                           rounded-full bg-white/10 text-white text-xs
                           transition-all duration-300 hover:bg-white/20"
                >
                  <Tag className="w-3 h-3" />
                  {tag}
                </span>
              ))}
            </div>

            {/* Local Testimonial */}
            {showTestimonial && isHovered && (
              <div 
                className="mt-4 p-3 rounded-lg bg-black/30 backdrop-blur-sm
                         transform transition-all duration-300"
              >
                <div className="flex items-start gap-2 text-white">
                  <Star className="w-5 h-5 text-yellow-400 flex-shrink-0 mt-1" />
                  <div>
                    <p className="text-sm italic mb-1">{project.testimonial.quote}</p>
                    <p className="text-xs text-gray-400">{project.testimonial.author}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Project Link */}
            <Link
              to={`/prosjekter/${project.id}`}
              className="inline-flex items-center gap-2 mt-4 text-white 
                       hover:text-green-400 transition-colors group/link"
            >
              <span className="text-sm font-medium">Utforsk prosjektet</span>
              <ChevronRight className="w-4 h-4 transform transition-transform 
                                   group-hover/link:translate-x-1" />
            </Link>
          </div>
        </div>
      </div>

      {/* Rich Local SEO Metadata */}
      <meta itemProp="dateCompleted" content={project.completionDate} />
      <meta itemProp="keywords" content={`${project.tags.join(', ')}, ${project.location}, landskapsarkitektur, hagedesign`} />
      <meta itemProp="locationCreated" content={`${project.location}, Ringerike, Buskerud`} />
      <meta itemProp="material" content={project.specifications.materials.join(', ')} />
      <meta itemProp="description" content={`${project.description} ${locationContext.short} ${locationContext.full}`} />
      <meta itemProp="creator" content="Ringerike Landskap - Din lokale anleggsgartner" />
    </article>
  );
};

export default ProjectCard; ```


#### `projects\ProjectFilter.tsx`

```tsx
import React from 'react';
import { motion } from 'framer-motion';
import { MapPin, Tag, Grid3X3 } from 'lucide-react';
import { ProjectFilterProps } from '../../types';

const ProjectFilter: React.FC<ProjectFilterProps> = ({
  categories,
  locations,
  tags,
  selectedFilters,
  onFilterChange
}) => {
  const FilterButton = ({ 
    type, 
    value, 
    isSelected 
  }: { 
    type: 'category' | 'location' | 'tag';
    value: string;
    isSelected: boolean;
  }) => (
    <motion.button
      onClick={() => onFilterChange(type, value)}
      className={`
        inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium
        transition-all duration-200 hover:shadow-md
        ${isSelected 
          ? 'bg-green-500 text-white shadow-lg scale-105' 
          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
        }
      `}
      whileHover={{ scale: isSelected ? 1.05 : 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      {type === 'location' && <MapPin className="w-4 h-4 mr-1.5" />}
      {type === 'tag' && <Tag className="w-4 h-4 mr-1.5" />}
      {type === 'category' && <Grid3X3 className="w-4 h-4 mr-1.5" />}
      {value}
    </motion.button>
  );

  return (
    <div className="space-y-6">
      {/* Categories */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium text-gray-500">Tjenester</h3>
        <div className="flex flex-wrap gap-2">
          {categories.map(category => (
            <FilterButton
              key={category}
              type="category"
              value={category}
              isSelected={selectedFilters.category === category}
            />
          ))}
        </div>
      </div>

      {/* Locations */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium text-gray-500">Områder</h3>
        <div className="flex flex-wrap gap-2">
          {locations.map(location => (
            <FilterButton
              key={location}
              type="location"
              value={location}
              isSelected={selectedFilters.location === location}
            />
          ))}
        </div>
      </div>

      {/* Tags */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium text-gray-500">Nøkkelord</h3>
        <div className="flex flex-wrap gap-2">
          {tags.map(tag => (
            <FilterButton
              key={tag}
              type="tag"
              value={tag}
              isSelected={selectedFilters.tag === tag}
            />
          ))}
        </div>
      </div>

      {/* Active Filters Summary - Screen Reader Only */}
      <div className="sr-only" role="status">
        {Object.entries(selectedFilters).map(([type, value]) => (
          value && (
            <span key={type}>
              Filtrerer etter {type}: {value}.
            </span>
          )
        ))}
      </div>
    </div>
  );
};

export default ProjectFilter; ```


#### `projects\ProjectGallery.tsx`

```tsx
import React, { useState } from 'react';
import { X } from 'lucide-react';
import { useProjectImages } from '@/lib/hooks/images';
import { IMAGE_CATEGORIES } from '@/lib/utils/images';
import { cn } from '@/lib/utils';

interface ProjectGalleryProps {
  category?: keyof typeof IMAGE_CATEGORIES;
  className?: string;
}

const ProjectGallery: React.FC<ProjectGalleryProps> = ({
  category,
  className
}) => {
  const { images, loading } = useProjectImages(category);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  if (loading) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="aspect-square bg-gray-200 rounded-lg animate-pulse"
          />
        ))}
      </div>
    );
  }

  return (
    <>
      <div className={cn(
        "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",
        className
      )}>
        {images.map((image, index) => (
          <button
            key={index}
            className="relative aspect-square overflow-hidden rounded-lg cursor-pointer group"
            onClick={() => setSelectedImage(image.path)}
          >
            <img
              src={image.path}
              alt={image.metadata?.title || ''}
              className="absolute inset-0 w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 className="text-sm font-medium">
                  {image.metadata?.title}
                </h3>
                {image.metadata?.description && (
                  <p className="text-xs mt-1 text-gray-200">
                    {image.metadata.description}
                  </p>
                )}
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <button
            className="absolute top-4 right-4 text-white p-2 hover:bg-white/10 rounded-full"
            onClick={() => setSelectedImage(null)}
          >
            <X className="w-6 h-6" />
          </button>
          <img
            src={selectedImage}
            alt="Project detail"
            className="max-w-full max-h-[90vh] rounded-lg"
          />
        </div>
      )}
    </>
  );
};

export default ProjectGallery;```


#### `projects\ProjectGrid.tsx`

```tsx
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ProjectGridProps } from '../../types';
import ProjectCard from './ProjectCard';
import ProjectFilter from './ProjectFilter';

const ProjectGrid: React.FC<ProjectGridProps> = ({ 
  projects, 
  filter,
  layout = 'grid' 
}) => {
  const [filteredProjects, setFilteredProjects] = useState(projects);
  const [selectedFilters, setSelectedFilters] = useState(filter || {});

  // Extract unique values for filter options
  const categories = [...new Set(projects.map(p => p.category))];
  const locations = [...new Set(projects.map(p => p.location))];
  const tags = [...new Set(projects.flatMap(p => p.tags))];

  useEffect(() => {
    let result = [...projects];

    if (selectedFilters.category) {
      result = result.filter(p => p.category === selectedFilters.category);
    }
    if (selectedFilters.location) {
      result = result.filter(p => p.location === selectedFilters.location);
    }
    if (selectedFilters.tag) {
      result = result.filter(p => p.tags.includes(selectedFilters.tag));
    }

    setFilteredProjects(result);
  }, [projects, selectedFilters]);

  const handleFilterChange = (type: 'category' | 'location' | 'tag', value: string) => {
    setSelectedFilters(prev => ({
      ...prev,
      [type]: prev[type] === value ? undefined : value
    }));
  };

  const gridLayouts = {
    grid: "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8",
    masonry: "columns-1 sm:columns-2 lg:columns-3 gap-6 sm:gap-8 [&>*]:mb-6 sm:[&>*]:mb-8",
    carousel: "flex gap-6 overflow-x-auto snap-x snap-mandatory pb-6 [&>*]:snap-center [&>*]:flex-shrink-0 [&>*]:w-[85vw] sm:[&>*]:w-[45vw] lg:[&>*]:w-[30vw]"
  };

  return (
    <section className="w-full">
      {/* Filters */}
      <div className="mb-8">
        <ProjectFilter
          categories={categories}
          locations={locations}
          tags={tags}
          selectedFilters={selectedFilters}
          onFilterChange={handleFilterChange}
        />
      </div>

      {/* Projects Grid */}
      <div className={gridLayouts[layout]}>
        <AnimatePresence mode="popLayout">
          {filteredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              layout
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <ProjectCard
                project={project}
                variant={index === 0 ? 'featured' : 'default'}
                showTestimonial={true}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Empty State */}
      {filteredProjects.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <p className="text-gray-500 text-lg">
            Ingen prosjekter funnet med valgte filtre.
          </p>
          <button
            onClick={() => setSelectedFilters({})}
            className="mt-4 text-green-600 hover:text-green-700 font-medium"
          >
            Nullstill filtre
          </button>
        </motion.div>
      )}

      {/* SEO Enhancement */}
      <div className="sr-only" role="contentinfo">
        <h2>Våre prosjekter i {locations.join(', ')}</h2>
        <p>
          Vi tilbyr følgende tjenester: {categories.join(', ')}. 
          Spesialisert i: {tags.join(', ')}.
        </p>
      </div>
    </section>
  );
};

export default ProjectGrid; ```


#### `seo\TestimonialsSchema.tsx`

```tsx
import React from "react";
import { TestimonialType } from "../features/testimonials/Testimonial";

interface TestimonialsSchemaProps {
    testimonials: TestimonialType[];
    companyName?: string;
    companyUrl?: string;
}

const TestimonialsSchema: React.FC<TestimonialsSchemaProps> = ({
    testimonials,
    companyName = "Ringerike Landskap",
    companyUrl = "https://ringerikelandskap.no",
}) => {
    // Calculate average rating
    const totalRating = testimonials.reduce(
        (sum, testimonial) => sum + testimonial.rating,
        0
    );
    const averageRating = totalRating / testimonials.length;

    // Create schema.org JSON-LD data
    const schemaData = {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        name: companyName,
        url: companyUrl,
        aggregateRating: {
            "@type": "AggregateRating",
            ratingValue: averageRating.toFixed(1),
            reviewCount: testimonials.length,
            bestRating: "5",
            worstRating: "1",
        },
        review: testimonials.map((testimonial) => ({
            "@type": "Review",
            author: {
                "@type": "Person",
                name: testimonial.name,
            },
            reviewRating: {
                "@type": "Rating",
                ratingValue: testimonial.rating,
                bestRating: "5",
                worstRating: "1",
            },
            reviewBody: testimonial.text,
            datePublished: new Date().toISOString().split("T")[0], // Current date in YYYY-MM-DD format
        })),
    };

    return (
        <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
        />
    );
};

export default TestimonialsSchema;
```


#### `ServiceCard.tsx`

```tsx
import React from 'react';
import { Link } from 'react-router-dom';

interface ServiceCardProps {
  title: string;
  description: string;
  image: string;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ title, description, image }) => {
  return (
    <div className="relative h-[400px] group overflow-hidden rounded-lg">
      <div 
        className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
        style={{ backgroundImage: `url(${image})` }}
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent" />
      <div className="absolute bottom-0 left-0 right-0 p-6 text-white transform transition-transform duration-300">
        <h3 className="text-2xl font-semibold mb-3">{title}</h3>
        <p className="text-gray-200 mb-4 line-clamp-3">{description}</p>
        <Link 
          to="#" 
          className="inline-block bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600 transition-colors"
        >
          Les mer
        </Link>
      </div>
    </div>
  );
};

export default ServiceCard;```


#### `services\Gallery.tsx`

```tsx
import React, { useState } from 'react';
import { GalleryImage } from '../../types';

interface GalleryProps {
  images: GalleryImage[];
}

const Gallery: React.FC<GalleryProps> = ({ images }) => {
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);

  return (
    <div>
      {/* Image grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {images.map((image, index) => (
          <div 
            key={index}
            className="relative aspect-square overflow-hidden rounded-lg cursor-pointer"
            onClick={() => setSelectedImage(image)}
          >
            <img
              src={image.url}
              alt={image.alt}
              className="absolute inset-0 w-full h-full object-cover transition-transform duration-300 hover:scale-110"
            />
            {image.caption && (
              <div className="absolute inset-x-0 bottom-0 bg-black/60 text-white p-2 text-sm">
                {image.caption}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Modal */}
      {selectedImage && (
        <div 
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="max-w-4xl w-full">
            <img
              src={selectedImage.url}
              alt={selectedImage.alt}
              className="w-full h-auto rounded-lg"
            />
            {selectedImage.caption && (
              <p className="text-white text-center mt-4">{selectedImage.caption}</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Gallery;```


#### `services\ServiceCard.tsx`

```tsx
import React from 'react';
import { Link } from 'react-router-dom';

interface ServiceCardProps {
  title: string;
  description: string;
  image: string;
  slug?: string;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ 
  title, 
  description, 
  image, 
  slug = '#' 
}) => {
  return (
    <div className="relative h-[400px] group overflow-hidden rounded-lg">
      <div 
        className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
        style={{ backgroundImage: `url(${image})` }}
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent" />
      <div className="absolute bottom-0 left-0 right-0 p-6 text-white transform transition-transform duration-300">
        <h3 className="text-2xl font-semibold mb-3">{title}</h3>
        <p className="text-gray-200 mb-4 line-clamp-3">{description}</p>
        <Link 
          to={`/tjenester/${slug}`}
          className="inline-block bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600 transition-colors"
        >
          Les mer
        </Link>
      </div>
    </div>
  );
};

export default ServiceCard;```


#### `shared\Elements\Card.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';
import { Image } from './Image';
import { Link } from './Link';

interface CardProps {
  title: string;
  description?: string;
  image?: string;
  link?: string;
  className?: string;
  children?: React.ReactNode;
  onClick?: () => void;
  aspectRatio?: 'square' | 'video' | 'portrait';
  variant?: 'default' | 'hover' | 'interactive';
}

const Card: React.FC<CardProps> = ({
  title,
  description,
  image,
  link,
  className,
  children,
  onClick,
  aspectRatio = 'square',
  variant = 'default'
}) => {
  const Component = link ? Link : onClick ? 'button' : 'div';
  const props = {
    ...(link && { to: link }),
    ...(onClick && { onClick }),
    className: cn(
      'group relative bg-white rounded-lg overflow-hidden transition-all duration-300',
      variant === 'hover' && 'hover:shadow-lg',
      variant === 'interactive' && 'cursor-pointer hover:-translate-y-1 hover:shadow-lg',
      className
    )
  };

  return (
    <Component {...props}>
      {image && (
        <Image
          src={image}
          alt={title}
          aspectRatio={aspectRatio}
          className="w-full"
        />
      )}
      <div className="p-4 sm:p-6">
        <h3 className="text-lg font-semibold mb-2">{title}</h3>
        {description && (
          <p className="text-gray-600 text-sm line-clamp-2 mb-4">{description}</p>
        )}
        {children}
      </div>
    </Component>
  );
};

export default Card;```


#### `shared\Elements\Form\index.ts`

```typescript
export { default as Input } from './Input';
export { default as Select } from './Select';
export { default as Textarea } from './Textarea';```


#### `shared\Elements\Form\Input.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helper?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ label, error, helper, leftIcon, rightIcon, className, ...props }, ref) => {
    const id = props.id || props.name;

    return (
      <div className="w-full">
        {label && (
          <label 
            htmlFor={id} 
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            {label}
          </label>
        )}
        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
              {leftIcon}
            </div>
          )}
          <input
            ref={ref}
            className={cn(
              'w-full rounded-md shadow-sm transition-colors duration-200',
              'border-gray-300 focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50',
              leftIcon && 'pl-10',
              rightIcon && 'pr-10',
              error && 'border-red-300 focus:border-red-500 focus:ring-red-500',
              className
            )}
            {...props}
          />
          {rightIcon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none text-gray-400">
              {rightIcon}
            </div>
          )}
        </div>
        {(error || helper) && (
          <p className={cn(
            'mt-1 text-sm',
            error ? 'text-red-600' : 'text-gray-500'
          )}>
            {error || helper}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;```


#### `shared\Elements\Form\Select.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown } from 'lucide-react';

interface Option {
  value: string;
  label: string;
}

interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'size'> {
  options: Option[];
  label?: string;
  error?: string;
  helper?: string;
  size?: 'sm' | 'md' | 'lg';
}

const Select = React.forwardRef<HTMLSelectElement, SelectProps>(
  ({ options, label, error, helper, size = 'md', className, ...props }, ref) => {
    const id = props.id || props.name;

    const sizeClasses = {
      sm: 'py-1.5 text-sm',
      md: 'py-2',
      lg: 'py-2.5 text-lg'
    };

    return (
      <div className="w-full">
        {label && (
          <label 
            htmlFor={id}
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            {label}
          </label>
        )}
        <div className="relative">
          <select
            ref={ref}
            className={cn(
              'w-full rounded-md shadow-sm appearance-none pr-10',
              'border-gray-300 focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50',
              error && 'border-red-300 focus:border-red-500 focus:ring-red-500',
              sizeClasses[size],
              className
            )}
            {...props}
          >
            {options.map(({ value, label }) => (
              <option key={value} value={value}>
                {label}
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <ChevronDown className="h-5 w-5 text-gray-400" />
          </div>
        </div>
        {(error || helper) && (
          <p className={cn(
            'mt-1 text-sm',
            error ? 'text-red-600' : 'text-gray-500'
          )}>
            {error || helper}
          </p>
        )}
      </div>
    );
  }
);

Select.displayName = 'Select';

export default Select;```


#### `shared\Elements\Form\Textarea.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helper?: string;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ label, error, helper, className, ...props }, ref) => {
    const id = props.id || props.name;

    return (
      <div className="w-full">
        {label && (
          <label 
            htmlFor={id}
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            {label}
          </label>
        )}
        <textarea
          ref={ref}
          className={cn(
            'w-full rounded-md shadow-sm transition-colors duration-200',
            'border-gray-300 focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50',
            error && 'border-red-300 focus:border-red-500 focus:ring-red-500',
            className
          )}
          {...props}
        />
        {(error || helper) && (
          <p className={cn(
            'mt-1 text-sm',
            error ? 'text-red-600' : 'text-gray-500'
          )}>
            {error || helper}
          </p>
        )}
      </div>
    );
  }
);

Textarea.displayName = 'Textarea';

export default Textarea;```


#### `shared\Elements\Icon.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';
import * as LucideIcons from 'lucide-react';

type IconName = keyof typeof LucideIcons;

interface IconProps {
  name: IconName;
  size?: 'sm' | 'md' | 'lg' | number;
  className?: string;
}

const Icon: React.FC<IconProps> = ({ name, size = 'md', className }) => {
  const IconComponent = LucideIcons[name];

  if (!IconComponent) {
    console.warn(`Icon "${name}" not found`);
    return null;
  }

  const sizeMap = {
    sm: 16,
    md: 24,
    lg: 32
  };

  const iconSize = typeof size === 'string' ? sizeMap[size] : size;

  return (
    <IconComponent
      size={iconSize}
      className={cn('flex-shrink-0', className)}
      aria-hidden="true"
    />
  );
};

export default Icon;```


#### `shared\Elements\Image.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface ImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  fallback?: string;
  aspectRatio?: 'square' | 'video' | 'portrait' | string;
}

const Image: React.FC<ImageProps> = ({
  src,
  alt,
  className,
  fallback = 'https://images.unsplash.com/photo-1558904541-efa843a96f01?auto=format&fit=crop&q=80',
  aspectRatio = 'square',
  ...props
}) => {
  const [error, setError] = React.useState(false);
  const [loading, setLoading] = React.useState(true);

  const handleError = () => {
    setError(true);
    setLoading(false);
  };

  const handleLoad = () => {
    setLoading(false);
  };

  const aspectRatioClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    portrait: 'aspect-[3/4]'
  };

  const ratioClass = aspectRatioClasses[aspectRatio as keyof typeof aspectRatioClasses] || aspectRatio;

  return (
    <div className={cn('relative overflow-hidden bg-gray-100', ratioClass, className)}>
      <img
        src={error ? fallback : src}
        alt={alt}
        className={cn(
          'absolute inset-0 w-full h-full object-cover transition-opacity duration-300',
          loading ? 'opacity-0' : 'opacity-100'
        )}
        onError={handleError}
        onLoad={handleLoad}
        {...props}
      />
      {loading && (
        <div className="absolute inset-0 bg-gray-100 animate-pulse" />
      )}
    </div>
  );
};

export default Image;```


#### `shared\Elements\index.ts`

```typescript
export { default as Button } from './Button';
export { default as Card } from './Card';
export { default as Icon } from './Icon';
export { default as Image } from './Image';
export { default as Link } from './Link';
export { default as Loading } from './Loading';
export { default as Logo } from './Logo';```


#### `shared\Elements\Link.tsx`

```tsx
import React from 'react';
import { Link as RouterLink, LinkProps as RouterLinkProps } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { trackEvent } from '@/lib/utils/analytics';

interface LinkProps extends RouterLinkProps {
  external?: boolean;
  tracking?: {
    category: string;
    action: string;
    label?: string;
  };
}

const Link: React.FC<LinkProps> = ({
  to,
  children,
  className,
  external,
  tracking,
  ...props
}) => {
  const handleClick = () => {
    if (tracking) {
      trackEvent({
        category: tracking.category,
        action: tracking.action,
        label: tracking.label
      });
    }
  };

  if (external) {
    return (
      <a
        href={to.toString()}
        target="_blank"
        rel="noopener noreferrer"
        className={cn(
          'transition-colors duration-200',
          'hover:text-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2',
          className
        )}
        onClick={handleClick}
        {...props}
      >
        {children}
      </a>
    );
  }

  return (
    <RouterLink
      to={to}
      className={cn(
        'transition-colors duration-200',
        'hover:text-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2',
        className
      )}
      onClick={handleClick}
      {...props}
    >
      {children}
    </RouterLink>
  );
};

export default Link;```


#### `shared\Elements\Loading.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const Loading: React.FC<LoadingProps> = ({ size = 'md', className }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  return (
    <div
      className={cn(
        'inline-flex items-center justify-center',
        className
      )}
      role="status"
      aria-label="Laster..."
    >
      <svg
        className={cn(
          'animate-spin text-green-500',
          sizeClasses[size]
        )}
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
      <span className="sr-only">Laster...</span>
    </div>
  );
};

export default Loading;```


#### `shared\ErrorBoundary.tsx`

```tsx
import React from 'react';
import { AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/Button';

interface Props {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onReset?: () => void;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends React.Component<Props, State> {
  public state: State = {
    hasError: false,
    error: undefined
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log error to your error reporting service
    console.error('Error caught by error boundary:', error, errorInfo);
    this.props.onError?.(error, errorInfo);
  }

  private handleReset = () => {
    this.setState({ hasError: false, error: undefined });
    this.props.onReset?.();
  };

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-[400px] flex flex-col items-center justify-center p-6 text-center">
          <div className="mb-6">
            <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Beklager, noe gikk galt</h2>
            <p className="text-gray-600 mb-4">
              Det oppstod en uventet feil. Vennligst prøv igjen eller kontakt oss hvis problemet vedvarer.
            </p>
            {this.state.error && (
              <pre className="mt-4 p-4 bg-gray-100 rounded-md text-sm text-left overflow-auto max-w-full">
                <code>{this.state.error.toString()}</code>
              </pre>
            )}
          </div>
          <Button onClick={this.handleReset} variant="primary">
            Prøv igjen
          </Button>
        </div>
      );
    }

    return this.props.children;
  }
} ```


#### `shared\Layout\index.ts`

```typescript
export { default as Layout } from './Layout';
export { default as Header } from './Header';
export { default as Footer } from './Footer';
export { default as Container } from './Container';
export { default as Meta } from './Meta';```


#### `shared\Layout\Layout.tsx`

```tsx
import React from 'react';
import { useAnalytics } from '@/lib/hooks';
import { Header } from './Header';
import { Footer } from './Footer';
import { Meta } from './Meta';

interface LayoutProps {
  children: React.ReactNode;
  meta?: {
    title?: string;
    description?: string;
    image?: string;
  };
}

const Layout: React.FC<LayoutProps> = ({ children, meta }) => {
  useAnalytics();

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <Meta {...meta} />
      <Header />
      <main className="flex-grow">{children}</main>
      <Footer />
    </div>
  );
};

export default Layout;```


#### `ui\Button.tsx`

```tsx
import React from "react";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";

type ButtonVariant = "primary" | "secondary" | "outline" | "text";
type ButtonSize = "sm" | "md" | "lg";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    variant?: ButtonVariant;
    size?: ButtonSize;
    fullWidth?: boolean;
    icon?: React.ReactNode;
    iconPosition?: "left" | "right";
}

interface LinkButtonProps extends Omit<ButtonProps, "onClick"> {
    to: string;
    external?: boolean;
}

const getVariantClasses = (variant: ButtonVariant): string => {
    switch (variant) {
        case "primary":
            return "bg-green-500 hover:bg-green-600 text-white shadow-sm";
        case "secondary":
            return "bg-gray-100 hover:bg-gray-200 text-gray-800 shadow-sm";
        case "outline":
            return "bg-transparent border border-green-500 text-green-500 hover:bg-green-50";
        case "text":
            return "bg-transparent text-green-500 hover:text-green-600 hover:bg-green-50";
        default:
            return "bg-green-500 hover:bg-green-600 text-white shadow-sm";
    }
};

const getSizeClasses = (size: ButtonSize): string => {
    switch (size) {
        case "sm":
            return "text-sm py-1.5 px-3";
        case "md":
            return "text-base py-2 px-4";
        case "lg":
            return "text-lg py-2.5 px-5";
        default:
            return "text-base py-2 px-4";
    }
};

export const Button: React.FC<ButtonProps> = ({
    children,
    variant = "primary",
    size = "md",
    fullWidth = false,
    icon,
    iconPosition = "left",
    className = "",
    ...props
}) => {
    const baseClasses =
        "inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2";
    const variantClasses = getVariantClasses(variant);
    const sizeClasses = getSizeClasses(size);
    const widthClass = fullWidth ? "w-full" : "";

    return (
        <button
            className={`${baseClasses} ${variantClasses} ${sizeClasses} ${widthClass} ${className}`}
            {...props}
        >
            {icon && iconPosition === "left" && (
                <span className="mr-2">{icon}</span>
            )}
            {children}
            {icon && iconPosition === "right" && (
                <span className="ml-2">{icon}</span>
            )}
        </button>
    );
};

export const LinkButton: React.FC<LinkButtonProps> = ({
    children,
    to,
    external = false,
    variant = "primary",
    size = "md",
    fullWidth = false,
    icon,
    iconPosition = "left",
    className = "",
    ...props
}) => {
    const baseClasses =
        "inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2";
    const variantClasses = getVariantClasses(variant);
    const sizeClasses = getSizeClasses(size);
    const widthClass = fullWidth ? "w-full" : "";

    if (external) {
        return (
            <a
                href={to}
                target="_blank"
                rel="noopener noreferrer"
                className={`${baseClasses} ${variantClasses} ${sizeClasses} ${widthClass} ${className}`}
                {...props}
            >
                {icon && iconPosition === "left" && (
                    <span className="mr-2">{icon}</span>
                )}
                {children}
                {icon && iconPosition === "right" && (
                    <span className="ml-2">{icon}</span>
                )}
            </a>
        );
    }

    return (
        <Link
            to={to}
            className={`${baseClasses} ${variantClasses} ${sizeClasses} ${widthClass} ${className}`}
            {...props}
        >
            {icon && iconPosition === "left" && (
                <span className="mr-2">{icon}</span>
            )}
            {children}
            {icon && iconPosition === "right" && (
                <span className="ml-2">{icon}</span>
            )}
        </Link>
    );
};

export default Button;
```


#### `ui\Container.tsx`

```tsx
import React from "react";

interface ContainerProps {
    children: React.ReactNode;
    className?: string;
    as?: React.ElementType;
    maxWidth?: "xs" | "sm" | "md" | "lg" | "xl" | "2xl" | "full";
}

const Container: React.FC<ContainerProps> = ({
    children,
    className = "",
    as: Component = "div",
    maxWidth = "xl",
}) => {
    const maxWidthClasses = {
        xs: "max-w-xs",
        sm: "max-w-sm",
        md: "max-w-md",
        lg: "max-w-lg",
        xl: "max-w-7xl",
        "2xl": "max-w-screen-2xl",
        full: "max-w-full",
    };

    return (
        <Component
            className={`mx-auto px-4 sm:px-6 lg:px-8 w-full ${maxWidthClasses[maxWidth]} ${className}`}
        >
            {children}
        </Component>
    );
};

export default Container;
```


#### `ui\Hero.tsx`

```tsx
import React from "react";
import { Link } from "react-router-dom";
import { ChevronDown } from "lucide-react";

export interface HeroProps {
    title: string;
    subtitle?: string;
    backgroundImage: string;
    location?: string;
    actionLink?: string;
    actionText?: string;
    height?: "small" | "medium" | "large" | "full";
    overlay?: "none" | "light" | "dark" | "gradient";
    textAlignment?: "left" | "center" | "right";
    textColor?: "light" | "dark";
}

const Hero: React.FC<HeroProps> = ({
    title,
    subtitle,
    backgroundImage = "/images/site/hero-main.webp",
    location = "Røyse, Hole kommune",
    actionLink,
    actionText = "Se våre prosjekter",
    height = "large",
    overlay = "gradient",
    textAlignment = "center",
    textColor = "light",
}) => {
    const heightClasses = {
        small: "h-[300px]",
        medium: "h-[500px]",
        large: "h-[calc(100vh-64px)] min-h-[600px] max-h-[800px]",
        full: "h-screen",
    };

    const overlayClasses = {
        none: "",
        light: "after:absolute after:inset-0 after:bg-white/30",
        dark: "after:absolute after:inset-0 after:bg-black/50",
        gradient:
            "after:absolute after:inset-0 after:bg-gradient-to-t after:from-black/70 after:to-transparent",
    };

    const textAlignmentClasses = {
        left: "text-left items-start",
        center: "text-center items-center",
        right: "text-right items-end",
    };

    const textColorClasses = {
        light: "text-white",
        dark: "text-gray-900",
    };

    const scrollToContent = () => {
        window.scrollTo({
            top: window.innerHeight,
            behavior: "smooth",
        });
    };

    return (
        <section
            className={`relative overflow-hidden ${heightClasses[height]} ${overlayClasses[overlay]}`}
        >
            {/* Background image */}
            <div
                className="absolute inset-0 transform scale-105"
                style={{
                    backgroundImage: `url(${backgroundImage})`,
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    backgroundAttachment: "fixed",
                }}
                role="img"
                aria-label="Landskapsprosjekt i Ringerike-området"
            />

            {/* Content */}
            <div className="relative z-10 h-full flex flex-col justify-center px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
                <div
                    className={`flex flex-col ${textAlignmentClasses[textAlignment]} gap-4 max-w-3xl mx-auto`}
                >
                    {location && (
                        <div className="inline-flex items-center bg-green-500/90 text-white px-4 py-1.5 rounded-full text-sm font-medium mb-2">
                            {location}
                        </div>
                    )}

                    <h1
                        className={`text-4xl sm:text-5xl lg:text-6xl font-bold ${textColorClasses[textColor]}`}
                    >
                        {title}
                    </h1>

                    {subtitle && (
                        <p
                            className={`text-lg sm:text-xl mt-2 ${textColorClasses[textColor]} opacity-90 max-w-2xl`}
                        >
                            {subtitle}
                        </p>
                    )}

                    {actionLink && (
                        <div className="mt-6">
                            <Link
                                to={actionLink}
                                className="inline-flex items-center bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-md font-medium transition-colors shadow-md"
                            >
                                {actionText}
                            </Link>
                        </div>
                    )}
                </div>

                {/* Scroll indicator */}
                <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
                    <button
                        onClick={scrollToContent}
                        className="flex flex-col items-center text-white opacity-80 hover:opacity-100 transition-opacity"
                        aria-label="Scroll ned for å se mer"
                    >
                        <span className="text-sm mb-2">Scroll ned</span>
                        <ChevronDown className="w-6 h-6 animate-bounce" />
                    </button>
                </div>
            </div>
        </section>
    );
};

export default Hero;```


#### `ui\Intersection.tsx`

```tsx
import React, { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

interface IntersectionProps {
  children: React.ReactNode;
  className?: string;
  threshold?: number;
  rootMargin?: string;
  onIntersect?: () => void;
  once?: boolean;
  as?: keyof JSX.IntrinsicElements;
}

export const Intersection: React.FC<IntersectionProps> = ({
  children,
  className,
  threshold = 0.1,
  rootMargin = '0px',
  onIntersect,
  once = true,
  as: Component = 'div'
}) => {
  const ref = useRef<HTMLElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          onIntersect?.();
          if (once && ref.current) {
            observer.unobserve(ref.current);
          }
        } else if (!once) {
          setIsVisible(false);
        }
      },
      {
        threshold,
        rootMargin
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [threshold, rootMargin, onIntersect, once]);

  return (
    <Component
      ref={ref}
      className={cn(
        'transition-opacity duration-700',
        isVisible ? 'opacity-100' : 'opacity-0',
        className
      )}
    >
      {children}
    </Component>
  );
};```


#### `ui\Logo.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface LogoProps {
  className?: string;
  color?: string;
  variant?: 'icon-only' | 'full' | 'text-only';
}

export const Logo: React.FC<LogoProps> = ({ 
  className,
  color = '#1d9545',
  variant = 'icon-only'
}) => {
  if (variant === 'text-only') {
    return (
      <svg 
        viewBox="0 0 1024 90" 
        className={cn('h-6', className)}
        aria-label="Ringerike Landskap logo text"
      >
        <path
          fill="currentColor"
          d="m32.4 54.1h-4.1v22.1h-14.8v-58.4h22.5q11.4 0 16.6 3.8 5.3 3.9 5.3 12.3c0.5 7-4.1 13.3-11 15 2.1 0.5 4 1.6 5.4 3.2 2 2.2 3.7 4.8 4.9 7.5l8 16.6h-15.7l-7-14.5c-0.9-2.3-2.4-4.4-4.3-6-1.7-1.2-3.7-1.7-5.8-1.6zm-4.1-10.4h6.2q4.6 0 6.6-1.8 2.1-1.8 2.1-5.8-0.1-4-2.1-5.7-2-1.7-6.6-1.7h-6.2zm47-25.9h14.8v58.4h-14.8zm30.5 58.4v-58.4h16.5l20.9 40.1v-40.1h14v58.4h-16.6l-20.8-40v40h-14zm118.6-32.4v28.1c-3.7 1.8-7.5 3.2-11.5 4.1-4 0.9-8.1 1.4-12.3 1.4q-14.3 0-22.7-8.2-8.3-8.1-8.3-22.1 0-14.1 8.5-22.2 8.5-8.1 23.3-8.1 5.6 0 11 1.1c3.4 0.7 6.7 1.8 9.9 3.2v12.1c-3-1.8-6.2-3.2-9.6-4.2-3-0.9-6.2-1.3-9.5-1.3q-8.8 0-13.6 5-4.7 5-4.7 14.4 0 9.3 4.6 14.3 4.6 5.1 13 5.1 2.2 0 4.3-0.3 1.8-0.3 3.6-1v-11.3h-9v-10.1h23zm14.3 32.4v-58.4h39.8v11.4h-25v10.9h23.5v11.4h-23.5v13.4h25.9v11.3h-40.7zm73.9-22.1h-4.2v22.2h-14.7v-58.5h22.5q11.4 0 16.6 3.9 5.3 3.9 5.3 12.3c0.5 7-4.2 13.3-11 15 2.1 0.5 4 1.6 5.4 3.1 2 2.3 3.6 4.8 4.9 7.6l8 16.6h-15.8l-6.9-14.6c-1-2.3-2.4-4.3-4.3-5.9-1.7-1.2-3.7-1.7-5.8-1.6zm-4.2-10.5h6.3q4.6 0 6.6-1.7 2-1.8 2-5.8 0-4-2-5.7-2-1.7-6.6-1.7h-6.3zm47.1 32.5v-58.4h14.8v58.4h-14.8zm30.5 0v-58.4h14.8v21.3l21.3-21.3h17.2l-27.7 27.6 30.5 30.8h-18.5l-22.8-23v23h-14.8zm62.2 0v-58.4h39.9v11.4h-25.1v10.8h23.6v11.4h-23.6v13.4h25.9v11.4h-40.7zm83.6 0v-58.4h14.8v47h26v11.4h-40.8zm86.2-10.6h-23.1l-3.7 10.6h-14.8l21.2-58.4h17.6l21.3 58.4h-14.9zm-19.5-10.8h15.7l-7.8-23.2zm46.8 21.4v-58.4h16.5l20.9 40.1v-40.1h13.9v58.4h-16.5l-20.8-40v40h-14zm67.1 0.1v-58.5h15.6q13 0 19.4 1.9c4.1 1.2 7.9 3.4 10.9 6.5 2.7 2.5 4.7 5.6 6 9.1 1.4 3.7 2.1 7.7 2 11.7 0.1 4-0.6 8-2 11.8-1.3 3.4-3.3 6.6-6 9.1-3.1 3.1-6.8 5.3-11 6.5q-6.5 1.9-19.3 1.9h-15.6zm20-47.1h-5.2v35.7h5.2q9.1 0 13.9-4.6 4.7-4.6 4.7-13.3 0-8.7-4.7-13.2-4.8-4.6-13.9-4.6zm76.1-11.7q5.2 0.8 10.3 2.2v12.4q-4.4-2.1-9.2-3.3-4.2-1-8.5-1.1c-2.7-0.2-5.4 0.3-7.8 1.5-1.7 0.9-2.7 2.7-2.6 4.6 0 1.4 0.6 2.8 1.7 3.7 1.9 1.1 4.1 1.9 6.3 2.2l6.2 1.3q9.6 2 13.6 6 4 3.9 4 11.3 0 9.7-5.6 14.4-5.6 4.7-17.1 4.7-5.6 0-11-1.1-5.6-1.1-10.9-3.1v-12.7c3.3 1.9 6.9 3.4 10.6 4.5 3.2 0.9 6.5 1.5 9.9 1.5 2.5 0.1 5.1-0.4 7.4-1.7q0.5-0.3 0.9-0.7c2.2-2.3 2.2-6-0.1-8.2q-1.8-1.5-7-2.6l-5.7-1.3q-8.6-1.9-12.6-6c-2.7-3-4.2-7-4-11.1q0-8.7 5.6-13.4 5.5-4.7 15.8-4.7 4.9 0 9.8 0.7zm28.3 58.8v-58.4h14.8v21.3l21.3-21.3h17.2l-27.7 27.6 30 30.3 21-57.9h17.6l21.3 58.4h-14.9l-3.6-10.7h-23.1l-3.7 10.7h-14.8-17.8l-22.8-23v23h-14.8zm77.6-21.5h15.7l-7.8-23.2zm46.7 21.4v-58.4h24.5q11 0 16.8 5 5.9 4.9 5.9 14.1 0 9.2-5.9 14.1-5.8 5-16.8 5h-9.7v20.3h-14.8zm14.8-47.6v16.4h8.2c2.4 0.1 4.8-0.6 6.6-2.2 3.1-3.4 3.1-8.6 0-12-1.9-1.6-4.2-2.3-6.6-2.2z"
        />
      </svg>
    );
  }

  if (variant === 'full') {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <svg 
          viewBox="0 0 1024 1024" 
          className="w-8 h-8"
          aria-hidden="true"
        >
          <path
            fill={color}
            d="M388.1 552.7l104.4 181-52.2 104.2 94.4 24.8-39.7 94.2 119.3-128.9-116.8-12.4 253.5-478.6 159 293.3v-488.3c0-52.6-42.8-95.2-95.4-95.2h-604.5c-52.6 0-95.3 42.6-95.4 95.2q0 0 0 0v856q0 1.7 0.1 3.4z"
          />
        </svg>
        <svg 
          viewBox="0 0 1024 90" 
          className="h-6"
          aria-hidden="true"
        >
          <path
            fill="currentColor"
            d="m32.4 54.1h-4.1v22.1h-14.8v-58.4h22.5q11.4 0 16.6 3.8 5.3 3.9 5.3 12.3c0.5 7-4.1 13.3-11 15 2.1 0.5 4 1.6 5.4 3.2 2 2.2 3.7 4.8 4.9 7.5l8 16.6h-15.7l-7-14.5c-0.9-2.3-2.4-4.4-4.3-6-1.7-1.2-3.7-1.7-5.8-1.6zm-4.1-10.4h6.2q4.6 0 6.6-1.8 2.1-1.8 2.1-5.8-0.1-4-2.1-5.7-2-1.7-6.6-1.7h-6.2zm47-25.9h14.8v58.4h-14.8zm30.5 58.4v-58.4h16.5l20.9 40.1v-40.1h14v58.4h-16.6l-20.8-40v40h-14zm118.6-32.4v28.1c-3.7 1.8-7.5 3.2-11.5 4.1-4 0.9-8.1 1.4-12.3 1.4q-14.3 0-22.7-8.2-8.3-8.1-8.3-22.1 0-14.1 8.5-22.2 8.5-8.1 23.3-8.1 5.6 0 11 1.1c3.4 0.7 6.7 1.8 9.9 3.2v12.1c-3-1.8-6.2-3.2-9.6-4.2-3-0.9-6.2-1.3-9.5-1.3q-8.8 0-13.6 5-4.7 5-4.7 14.4 0 9.3 4.6 14.3 4.6 5.1 13 5.1 2.2 0 4.3-0.3 1.8-0.3 3.6-1v-11.3h-9v-10.1h23zm14.3 32.4v-58.4h39.8v11.4h-25v10.9h23.5v11.4h-23.5v13.4h25.9v11.3h-40.7zm73.9-22.1h-4.2v22.2h-14.7v-58.5h22.5q11.4 0 16.6 3.9 5.3 3.9 5.3 12.3c0.5 7-4.2 13.3-11 15 2.1 0.5 4 1.6 5.4 3.1 2 2.3 3.6 4.8 4.9 7.6l8 16.6h-15.8l-6.9-14.6c-1-2.3-2.4-4.3-4.3-5.9-1.7-1.2-3.7-1.7-5.8-1.6zm-4.2-10.5h6.3q4.6 0 6.6-1.7 2-1.8 2-5.8 0-4-2-5.7-2-1.7-6.6-1.7h-6.3zm47.1 32.5v-58.4h14.8v58.4h-14.8zm30.5 0v-58.4h14.8v21.3l21.3-21.3h17.2l-27.7 27.6 30.5 30.8h-18.5l-22.8-23v23h-14.8zm62.2 0v-58.4h39.9v11.4h-25.1v10.8h23.6v11.4h-23.6v13.4h25.9v11.4h-40.7zm83.6 0v-58.4h14.8v47h26v11.4h-40.8zm86.2-10.6h-23.1l-3.7 10.6h-14.8l21.2-58.4h17.6l21.3 58.4h-14.9zm-19.5-10.8h15.7l-7.8-23.2zm46.8 21.4v-58.4h16.5l20.9 40.1v-40.1h13.9v58.4h-16.5l-20.8-40v40h-14zm67.1 0.1v-58.5h15.6q13 0 19.4 1.9c4.1 1.2 7.9 3.4 10.9 6.5 2.7 2.5 4.7 5.6 6 9.1 1.4 3.7 2.1 7.7 2 11.7 0.1 4-0.6 8-2 11.8-1.3 3.4-3.3 6.6-6 9.1-3.1 3.1-6.8 5.3-11 6.5q-6.5 1.9-19.3 1.9h-15.6zm20-47.1h-5.2v35.7h5.2q9.1 0 13.9-4.6 4.7-4.6 4.7-13.3 0-8.7-4.7-13.2-4.8-4.6-13.9-4.6zm76.1-11.7q5.2 0.8 10.3 2.2v12.4q-4.4-2.1-9.2-3.3-4.2-1-8.5-1.1c-2.7-0.2-5.4 0.3-7.8 1.5-1.7 0.9-2.7 2.7-2.6 4.6 0 1.4 0.6 2.8 1.7 3.7 1.9 1.1 4.1 1.9 6.3 2.2l6.2 1.3q9.6 2 13.6 6 4 3.9 4 11.3 0 9.7-5.6 14.4-5.6 4.7-17.1 4.7-5.6 0-11-1.1-5.6-1.1-10.9-3.1v-12.7c3.3 1.9 6.9 3.4 10.6 4.5 3.2 0.9 6.5 1.5 9.9 1.5 2.5 0.1 5.1-0.4 7.4-1.7q0.5-0.3 0.9-0.7c2.2-2.3 2.2-6-0.1-8.2q-1.8-1.5-7-2.6l-5.7-1.3q-8.6-1.9-12.6-6c-2.7-3-4.2-7-4-11.1q0-8.7 5.6-13.4 5.5-4.7 15.8-4.7q4.9 0 9.8 0.7zm28.3 58.8v-58.4h14.8v21.3l21.3-21.3h17.2l-27.7 27.6 30 30.3 21-57.9h17.6l21.3 58.4h-14.9l-3.6-10.7h-23.1l-3.7 10.7h-14.8-17.8l-22.8-23v23h-14.8zm77.6-21.5h15.7l-7.8-23.2zm46.7 21.4v-58.4h24.5q11 0 16.8 5 5.9 4.9 5.9 14.1 0 9.2-5.9 14.1-5.8 5-16.8 5h-9.7v20.3h-14.8zm14.8-47.6v16.4h8.2c2.4 0.1 4.8-0.6 6.6-2.2 3.1-3.4 3.1-8.6 0-12-1.9-1.6-4.2-2.3-6.6-2.2z"
          />
        </svg>
      </div>
    );
  }

  // Default icon-only variant
  return (
    <svg 
      viewBox="0 0 1024 1024" 
      className={cn('w-8 h-8', className)}
      aria-label="Ringerike Landskap logo"
    >
      <path
        fill={color}
        d="M388.1 552.7l104.4 181-52.2 104.2 94.4 24.8-39.7 94.2 119.3-128.9-116.8-12.4 253.5-478.6 159 293.3v-488.3c0-52.6-42.8-95.2-95.4-95.2h-604.5c-52.6 0-95.3 42.6-95.4 95.2q0 0 0 0v856q0 1.7 0.1 3.4z"
      />
    </svg>
  );
};```


#### `ui\Notifications.tsx`

```tsx
import React from 'react';
import { X, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { useApp } from '@/lib/context/AppContext';
import { cn } from '@/lib/utils';

export const Notifications: React.FC = () => {
  const { state, removeNotification } = useApp();
  const { notifications } = state;

  if (notifications.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-50 flex flex-col gap-2 max-w-md">
      {notifications.map(notification => (
        <div
          key={notification.id}
          className={cn(
            'p-4 rounded-lg shadow-lg flex items-start gap-3 animate-fade-in',
            notification.type === 'error' && 'bg-red-50 text-red-800 border border-red-200',
            notification.type === 'success' && 'bg-green-50 text-green-800 border border-green-200',
            notification.type === 'info' && 'bg-blue-50 text-blue-800 border border-blue-200'
          )}
        >
          <div className="flex-shrink-0">
            {notification.type === 'error' && <AlertCircle className="w-5 h-5 text-red-500" />}
            {notification.type === 'success' && <CheckCircle className="w-5 h-5 text-green-500" />}
            {notification.type === 'info' && <Info className="w-5 h-5 text-blue-500" />}
          </div>
          <div className="flex-1">
            <p className="text-sm">{notification.message}</p>
          </div>
          <button
            onClick={() => removeNotification(notification.id)}
            className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Close notification"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      ))}
    </div>
  );
}; ```


#### `ui\Skeleton.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface SkeletonProps {
  className?: string;
  animate?: boolean;
}

export const Skeleton: React.FC<SkeletonProps> = ({ 
  className,
  animate = true
}) => {
  return (
    <div
      className={cn(
        'bg-gray-200 rounded-md',
        animate && 'animate-pulse',
        className
      )}
    />
  );
};```


#### `ui\Transition.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface TransitionProps {
  children: React.ReactNode;
  show?: boolean;
  appear?: boolean;
  className?: string;
  type?: 'fade' | 'slide-up' | 'slide-down' | 'slide-left' | 'slide-right';
  duration?: 'fast' | 'normal' | 'slow';
}

export const Transition: React.FC<TransitionProps> = ({
  children,
  show = true,
  appear = false,
  className,
  type = 'fade',
  duration = 'normal'
}) => {
  const [isVisible, setIsVisible] = React.useState(show && !appear);

  React.useEffect(() => {
    if (show) {
      setIsVisible(true);
    }
  }, [show]);

  const handleTransitionEnd = () => {
    if (!show) {
      setIsVisible(false);
    }
  };

  const baseStyles = 'transition-all';
  
  const durationStyles = {
    fast: 'duration-200',
    normal: 'duration-300',
    slow: 'duration-500'
  };

  const typeStyles = {
    fade: 'opacity-0',
    'slide-up': 'opacity-0 translate-y-4',
    'slide-down': 'opacity-0 -translate-y-4',
    'slide-left': 'opacity-0 translate-x-4',
    'slide-right': 'opacity-0 -translate-x-4'
  };

  const visibleStyles = {
    fade: 'opacity-100',
    'slide-up': 'opacity-100 translate-y-0',
    'slide-down': 'opacity-100 translate-y-0',
    'slide-left': 'opacity-100 translate-x-0',
    'slide-right': 'opacity-100 translate-x-0'
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div
      className={cn(
        baseStyles,
        durationStyles[duration],
        show ? visibleStyles[type] : typeStyles[type],
        className
      )}
      onTransitionEnd={handleTransitionEnd}
      role="presentation"
    >
      {children}
    </div>
  );
};```
