
Please propose the most optimal configuration, example:

	# Dir `depcruise-config`

	*Files marked `[-]` are shown in structure but not included in content.

	### File Structure

	```
	├── depcruise-config.js
	└── package.json
	```

	#### `depcruise-config.js`

	```javascript
	/** @type {import('dependency-cruiser').IConfiguration} */
	module.exports = {
	  options: {
	    doNotFollow: {
	      dependencyTypes: [
	        "npm",
	        "npm-dev",
	        "npm-optional",
	        "npm-peer",
	        "npm-bundled",
	        "npm-no-pkg",
	      ],
	    },

	    includeOnly: "^src",

	    tsPreCompilationDeps: false,

	    tsConfig: {
	      fileName: "./tsconfig.json",
	    },

	    /* How to resolve external modules - use "yarn-pnp" if you're using yarn's Plug'n'Play.
	       otherwise leave it out (or set to the default, which is 'node_modules')
	    */
	    externalModuleResolutionStrategy: "yarn-pnp",

	    progress: { type: "performance-log" },

	    reporterOptions: {
	      archi: {
	        collapsePattern:
	          "^src/app/[^/]+|^src/pages/[^/]+|^src/features/[^/]+|^src/ui-kit/[^/]+",

	        theme: {
	          modules: [
	            {
	              criteria: { collapsed: true },
	              attributes: { shape: "tab" },
	            },
	            {
	              criteria: { source: "^src/app/[^/]+" },
	              attributes: { fillcolor: "#ffbdbd" },
	            },
	            {
	              criteria: { source: "^src/pages/[^/]+" },
	              attributes: { fillcolor: "#ffd9a3" },
	            },
	            {
	              criteria: { source: "^src/features/[^/]+" },
	              attributes: { fillcolor: "#aedaff" },
	            },
	            {
	              criteria: { source: "^src/ui-kit/[^/]+" },
	              attributes: { fillcolor: "#efefef" },
	            },
	          ],
	          graph: {
	            splines: "ortho",
	            rankdir: "TB",
	            ranksep: "1",
	          },
	        },
	      },
	    },
	  },
	};
	```

	#### `package.json`

	```json
	{
	  "scripts": {
	    "depcruise:archi": "depcruise --version && depcruise --config depcruise-config.js --output-type archi src | dot -T svg | depcruise-wrap-stream-in-html > high-level-dependencies.html"
	  }
	}```