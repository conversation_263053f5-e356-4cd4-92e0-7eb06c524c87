please fix the file not founds:

      <h2>Interactive Visualizations</h2><div class="grid">
            <div class="card featured">
              <h3>D3 Interactive Graph</h3>
              <p>Advanced interactive visualization with D3.js featuring multiple layouts and filtering options</p>
              <p class="warning">File not found: interactive/d3-graph.html</p>
            </div>

            <div class="card ">
              <h3>Dependency Graph (HTML)</h3>
              <p>Interactive dependency graph showing all module relationships</p>
              <p class="warning">File not found: interactive/dependency-graph.html</p>
            </div>

            <div class="card ">
              <h3>High Level Dependencies</h3>
              <p>Architectural overview of module relationships</p>
              <p class="warning">File not found: interactive/high-level-dependencies.html</p>
            </div>

            <div class="card ">
              <h3>Architecture Interactive View</h3>
              <p>Interactive visualization of the codebase architecture</p>
              <p class="warning">File not found: interactive/archi-interactive.html</p>
            </div>

            <div class="card ">
              <h3>Validation Report</h3>
              <p>Validation results showing dependency rule violations</p>
              <p class="warning">File not found: interactive/validation.html</p>
            </div>

            <div class="card ">
              <h3>Circle Packing Visualization</h3>
              <p>Interactive zoomable circle packing visualization grouped by module types.</p>
              <p class="warning">File not found: interactive/circle-packing.html</p>
            </div>

            <div class="card ">
              <h3>Bubble Chart</h3>
              <p>Non-hierarchical bubble chart showing module importance by size and categorized by color</p>
              <p class="warning">File not found: interactive/bubble-chart.html</p>
            </div>

            <div class="card ">
              <h3>Flow Diagram</h3>
              <p>Interactive directed graph showing dependency flow between modules with multiple layout options</p>
              <a href="interactive/flow-diagram.html" target="_blank">View</a>
            </div>
          </div><h2>Static Visualizations (SVG)</h2><div class="grid">
            <div class="card ">
              <h3>Basic Dependency Graph</h3>
              <p>Simple dependency graph in SVG format</p>
              <p class="warning">File not found: graphs/dependency-graph.svg</p>
            </div>

            <div class="card ">
              <h3>Hierarchical Layout</h3>
              <p>Top-to-bottom hierarchical visualization</p>
              <p class="warning">File not found: graphs/hierarchical-graph.svg</p>
            </div>

            <div class="card ">
              <h3>Circular Layout</h3>
              <p>Circular layout visualization showing module relationships</p>
              <p class="warning">File not found: graphs/circular-graph.svg</p>
            </div>

            <div class="card ">
              <h3>Clustered Layout</h3>
              <p>Clustered visualization grouping related modules</p>
              <p class="warning">File not found: graphs/clustered-graph.svg</p>
            </div>

            <div class="card ">
              <h3>Tech-Filtered View</h3>
              <p>Visualization focusing on specific technical components</p>
              <p class="warning">File not found: graphs/tech-filtered.svg</p>
            </div>
          </div><h2>Data</h2><div class="grid">
            <div class="card ">
              <h3>Dependency Data (JSON)</h3>
              <p>Raw dependency data in JSON format</p>
              <p class="warning">File not found: data/dependency-data.json</p>
            </div>
