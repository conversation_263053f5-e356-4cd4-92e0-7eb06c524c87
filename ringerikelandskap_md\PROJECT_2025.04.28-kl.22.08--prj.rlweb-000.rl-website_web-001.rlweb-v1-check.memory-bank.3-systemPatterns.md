# System Patterns: Ringerike Landskap Website

## Component Hierarchy Principles

### 1. Domain-First Organization
- Components are organized by business domain (Projects, Services, Testimonials)
- Each domain has its own types, components, hooks, and utilities
- Cross-domain dependencies are clearly managed through public interfaces

### 2. Feature Composition Pattern
```
feature/
├── components/   # Feature-specific UI components
├── hooks/        # Feature-specific hooks to manage state and behavior
├── api.ts        # Feature-specific API operations
├── types.ts      # Feature-specific types
└── index.ts      # Public interface exposing only what's needed
```

### 3. Shared Components Classification
- **Foundation:** Base UI primitives with no business logic (Button, Container)
- **Composition:** Combinations of foundation components (Card, NavMenu)
- **Integration:** Components that connect foundation to data (ServiceList, ProjectGrid)

### 4. Component Design Rules
- Components should be single-responsibility
- Data fetching happens outside components via hooks
- Common UI patterns are abstracted into reusable components
- Components accept properly typed props and handle their own internal state

## Data Flow Intentions

### 1. Unidirectional Data Flow
- Data flows from parent to child through props
- State is managed at the appropriate feature level
- Hooks encapsulate data fetching logic from API

### 2. Type-Driven Development
- All data structures have explicit TypeScript interfaces
- Domain types drive component props
- API responses mapped to domain types

### 3. API Layer Architecture
```
api/
├── client.ts        # Base API client configuration
├── endpoints/       # API endpoint definitions by domain
│   ├── projects.ts
│   └── services.ts
└── index.ts         # Public API interface
```

### 4. State Management Strategy
- Local component state for UI interactions
- React Context for cross-component state (minimal use)
- Custom hooks for domain state management

## Seasonal Content Flow Reasoning

### 1. Dynamic Content Strategy
- Site adapts content based on current Norwegian season
- Service highlights change throughout the year
- Project showcases filter by seasonal relevance

### 2. Content Flow Pattern
```
Browser Loads → Detect Season → Load Seasonal Content → Adapt UI
```

### 3. Seasonal Detection Logic
- Based on current date in Norway timezone
- Maps months to appropriate seasons
- Provides fallback for edge cases

### 4. Photo & Gallery Management
- Projects organized by category and season
- Service images selected based on seasonal relevance
- Image optimization pipeline for performance
