# File Structure Consolidation Plan

## Current Redundancies Identified

1. **Duplicate Utility Directories**:
   - `src/utils/` and `src/lib/utils/` contain overlapping functionality
   - Image handling spread across multiple files (`imageLoader.ts` and `images/index.ts`)
   - Analytics implemented in two locations

2. **Duplicate Hooks Directories**:
   - `src/hooks/` and `src/lib/hooks/` with similar functionality

3. **Inconsistent Content Organization**:
   - Some content in JSON, some in TypeScript files
   - Partial migration to JSON-driven architecture

4. **Redundant Type Definitions**:
   - Types defined in multiple locations
   - Inconsistent type usage

## Consolidation Strategy

### 1. Canonical Directory Structure

| Type | Canonical Location | Purpose |
|------|-------------------|---------|
| Utilities | `src/lib/utils/` | All utility functions |
| Hooks | `src/hooks/` | All React hooks |
| Types | `src/types/` | All TypeScript type definitions |
| Content | `src/data/json/` | All JSON content files |
| Components | Current structure | No change needed |
| Pages | Current structure | No change needed |

### 2. Re-export Strategy for Backward Compatibility

- Create re-export files that reference the canonical locations
- Phase out these re-exports in future iterations

### 3. Implementation Phases

#### Phase 1: Utils Consolidation
- Move all utilities to `src/lib/utils/`
- Consolidate image utilities into a single approach
- Update imports to reference new locations

#### Phase 2: Hooks Consolidation
- Move all hooks to `src/hooks/`
- Update imports to reference new locations

#### Phase 3: Type Consolidation
- Centralize types in `src/types/`
- Create a consistent type export pattern

#### Phase 4: Content Organization
- Complete the JSON migration for all content
- Standardize on a single data loading approach

## Implementation Approach

For each consolidation task:

1. Identify the canonical implementation
2. Create directory structure if needed
3. Move/consolidate files
4. Create re-export files for backward compatibility
5. Test to ensure functionality is preserved
