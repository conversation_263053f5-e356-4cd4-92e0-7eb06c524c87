# Dir `devnotes`

*Files marked `[-]` are shown in structure but not included in content.

### File Structure

```
├── architecture.md
├── components.md
├── data-structure.md
├── hooks-and-utils.md
├── responsive-design.md
├── seasonal-adaptation.md
├── sitemap.md
├── techstack.md
└── tldr.md
```


#### `architecture.md`

```markdown
# Architecture Overview

## Project Structure

The Ringerike Landskap website follows a well-organized structure that separates concerns and promotes maintainability:

```
/src
├── components/           # Reusable UI components
│   ├── (sections)/       # Page-specific sections
│   ├── common/           # Shared components
│   ├── layout/           # Layout components (Header, Footer, etc.)
│   ├── ui/               # Basic UI components (Button, Container, etc.)
│
├── features/             # Feature-specific components and logic
│   ├── home/             # Home page features
│   ├── projects/         # Project-related features
│   ├── services/         # Service-related features
│   ├── testimonials/     # Testimonial-related features
│
├── lib/                  # Core utilities and configurations
│   ├── config/           # Configuration files
│   ├── context/          # React context providers
│   ├── hooks/            # Custom React hooks
│   ├── types/            # TypeScript type definitions
│   ├── utils/            # Utility functions
│
├── pages/                # Page components
│   ├── about/            # About page
│   ├── contact/          # Contact page
│   ├── home/             # Home page
│   ├── projects/         # Projects pages
│   ├── services/         # Services pages
│   ├── testimonials/     # Testimonials page
│
├── styles/               # Global styles
│   ├── animations.css    # Animation styles
│   ├── base.css          # Base styles
│   ├── utilities.css     # Utility classes
│
├── data/                 # Static data
│   ├── projects.ts       # Project data
│   ├── services.ts       # Service data
│   ├── team.ts           # Team data
│   ├── testimonials.ts   # Testimonial data
│
├── utils/                # Utility functions
│   ├── imageLoader.ts    # Image loading utilities
```

## Architectural Patterns

### Component Architecture

The project follows a component-based architecture with a clear hierarchy:

1. **UI Components**: Basic, reusable UI elements like buttons, containers, etc.
2. **Common Components**: Shared components used across multiple pages
3. **Section Components**: Larger components that make up sections of pages
4. **Feature Components**: Components specific to a particular feature
5. **Page Components**: Top-level components that represent entire pages

### Data Flow

Data flows through the application in a predictable manner:

1. **Static Data**: Stored in `/data` directory
2. **Component Props**: Data is passed down through props
3. **Hooks**: Custom hooks manage state and side effects
4. **Context**: Global state is managed through React Context

### Responsive Design

The website is fully responsive, using:

1. **Tailwind CSS**: For responsive utility classes
2. **Media Queries**: For component-specific responsive behavior
3. **Responsive Components**: Components adapt to different screen sizes

### Seasonal Adaptation

A key architectural feature is the seasonal adaptation:

1. **Current Season Detection**: `getCurrentSeason()` utility function
2. **Seasonal Content Mapping**: Maps seasons to relevant content
3. **Seasonal UI Components**: Components that adapt based on season
4. **Seasonal Filtering**: Filtering of projects and services by season

## Key Design Patterns

### Component Composition

Components are composed from smaller, reusable pieces:

```tsx
// Example of component composition
<Container>
  <Hero title="..." subtitle="..." />
  <ServiceGrid services={seasonalServices} />
  <TestimonialsSection testimonials={testimonials} />
</Container>
```

### Custom Hooks

Custom hooks encapsulate reusable logic:

```tsx
// Example of custom hook
const { projects, loading } = useProjects();
```

### Utility Functions

Pure utility functions handle common operations:

```tsx
// Example of utility function
const formattedDate = formatDate(project.completionDate);
```

### Type Safety

TypeScript is used throughout the project for type safety:

```tsx
// Example of TypeScript interface
interface ProjectType {
  id: string;
  title: string;
  description: string;
  // ...
}
```

## Performance Considerations

1. **Image Optimization**: WebP format for images
2. **Code Splitting**: Each page is a separate chunk
3. **Lazy Loading**: Images and components are lazy loaded
4. **Memoization**: `useMemo` and `useCallback` for expensive operations

## Accessibility

1. **Semantic HTML**: Proper use of HTML elements
2. **ARIA Attributes**: For enhanced accessibility
3. **Keyboard Navigation**: All interactive elements are keyboard accessible
4. **Screen Reader Support**: Proper labels and descriptions

## SEO Optimization

1. **Meta Tags**: Dynamic meta tags for each page
2. **Structured Data**: Schema.org markup for rich results
3. **Semantic HTML**: Proper heading structure
4. **Canonical URLs**: Proper URL structure

## Deployment Strategy

The website is built with Vite and deployed to Netlify:

1. **Build Process**: TypeScript compilation and bundling
2. **Static Site Generation**: Pre-rendered HTML
3. **CDN Distribution**: Fast global access
4. **Environment Variables**: For configuration```


#### `components.md`

```markdown
# Components Documentation

## UI Components

### Button

The Button component is a versatile, reusable button that supports multiple variants and can act as either a button or a link.

```tsx
<Button
  variant="primary"
  size="lg"
  to="/kontakt"
>
  Kontakt oss
</Button>
```

**Props:**
- `variant`: 'primary' | 'secondary' | 'outline' | 'text'
- `size`: 'sm' | 'md' | 'lg'
- `to`: Optional URL for link behavior
- `fullWidth`: Boolean to make button full width
- `icon`: Optional icon component
- `iconPosition`: 'left' | 'right'

### Container

The Container component provides consistent width constraints and padding.

```tsx
<Container size="lg" className="py-12">
  {children}
</Container>
```

**Props:**
- `size`: 'sm' | 'md' | 'lg' | 'xl'
- `as`: React element type (default: 'div')
- `padded`: Boolean to add padding

### Hero

The Hero component is used for page headers with background images and call-to-action buttons.

```tsx
<Hero
  title="Anleggsgartner & maskinentreprenør"
  subtitle="Med base på Røyse skaper vi varige uterom..."
  backgroundImage="/images/site/hero-main.webp"
  location="Røyse, Hole kommune"
  actionLink="/prosjekter"
  actionText="Se våre prosjekter"
/>
```

**Props:**
- `title`: Main heading
- `subtitle`: Optional subheading
- `backgroundImage`: URL for background image
- `location`: Optional location text
- `yearEstablished`: Optional year text
- `actionLink`: Optional CTA link
- `actionText`: Optional CTA text
- `height`: 'small' | 'medium' | 'large' | 'full'
- `overlay`: 'none' | 'light' | 'dark' | 'gradient'
- `textAlignment`: 'left' | 'center' | 'right'
- `textColor`: 'light' | 'dark'

### ServiceAreaList

Displays a list of service areas with their distances and descriptions.

```tsx
<ServiceAreaList areas={serviceAreas} />
```

**Props:**
- `areas`: Array of ServiceArea objects
- `className`: Optional CSS class

### SeasonalCTA

A call-to-action component that adapts based on the current season.

```tsx
<SeasonalCTA season="summer" />
```

**Props:**
- `season`: 'spring' | 'summer' | 'fall' | 'winter'
- `className`: Optional CSS class

## Feature Components

### ProjectCard

Displays a project with image, title, description, and metadata.

```tsx
<ProjectCard
  project={project}
  variant="featured"
  showTestimonial={true}
/>
```

**Props:**
- `project`: ProjectType object
- `variant`: 'default' | 'featured' | 'compact'
- `showTestimonial`: Boolean to show testimonial
- `onProjectClick`: Optional click handler

### ProjectsCarousel

A carousel component for displaying projects.

```tsx
<ProjectsCarousel projects={recentProjects} />
```

**Props:**
- `projects`: Array of ProjectType objects
- `className`: Optional CSS class

### SeasonalProjectsCarousel

A carousel that filters and displays projects relevant to the current season.

```tsx
<SeasonalProjectsCarousel projects={recentProjects} />
```

**Props:**
- `projects`: Array of ProjectType objects
- `className`: Optional CSS class

### ServiceCard

Displays a service with image, title, description, and features.

```tsx
<ServiceCard
  service={service}
  variant="compact"
/>
```

**Props:**
- `service`: ServiceType object
- `variant`: 'default' | 'compact' | 'featured'
- `className`: Optional CSS class

### SeasonalServicesSection

A section that displays services relevant to the current season.

```tsx
<SeasonalServicesSection />
```

### TestimonialsSection

Displays testimonials in a slider format.

```tsx
<TestimonialsSection testimonials={testimonials} />
```

**Props:**
- `testimonials`: Array of TestimonialType objects
- `className`: Optional CSS class

## Layout Components

### Header

The main navigation header with responsive menu.

```tsx
<Header />
```

### Footer

The footer with contact information, links, and copyright.

```tsx
<Footer />
```

## Page Components

### HomePage

The main landing page with seasonal adaptation.

```tsx
<HomePage />
```

### ServicesPage

The services page with filtering capabilities.

```tsx
<ServicesPage />
```

### ProjectsPage

The projects page with filtering capabilities.

```tsx
<ProjectsPage />
```

### ServiceDetailPage

Detailed view of a specific service.

```tsx
<ServiceDetailPage />
```

**URL Parameters:**
- `id`: Service ID

### ProjectDetailPage

Detailed view of a specific project.

```tsx
<ProjectDetailPage />
```

**URL Parameters:**
- `id`: Project ID

### AboutPage

Information about the company and team.

```tsx
<AboutPage />
```

### ContactPage

Contact form and information.

```tsx
<ContactPage />
```

### TestimonialsPage

Customer testimonials with filtering.

```tsx
<TestimonialsPage />
```

## Component Relationships

The components are organized in a hierarchical structure:

1. **Page Components** use **Layout Components** for structure
2. **Page Components** use **Feature Components** for functionality
3. **Feature Components** use **UI Components** for presentation
4. **UI Components** are the building blocks for all other components

This structure ensures:
- **Reusability**: Components can be reused across the application
- **Maintainability**: Changes to one component don't affect others
- **Consistency**: UI elements are consistent throughout the application
- **Scalability**: New features can be added without modifying existing code```


#### `data-structure.md`

```markdown
# Data Structure Documentation

## Core Data Types

### ProjectType

Represents a landscaping project with details about the work performed.

```typescript
interface ProjectType {
  id: string;                 // Unique identifier
  title: string;              // Project title
  description: string;        // Project description
  location: string;           // Location (city/area)
  completionDate: string;     // When the project was completed
  image: string;              // Main image URL
  category: string;           // Project category (e.g., "Belegningsstein")
  tags: string[];             // Related tags for filtering
  specifications: {
    size: string;             // Project size (e.g., "280m²")
    duration: string;         // Project duration (e.g., "6 uker")
    materials: string[];      // Materials used
    features: string[];       // Special features
  };
  testimonial?: {             // Optional testimonial
    quote: string;            // Customer quote
    author: string;           // Customer name
  };
}
```

### ServiceType

Represents a service offered by the company.

```typescript
interface ServiceType {
  id: string;                 // Unique identifier
  title: string;              // Service title
  description: string;        // Service description
  image: string;              // Main image URL
  longDescription?: string;   // Optional extended description
  features: string[];         // Service features/benefits
  imageCategory?: string;     // Related image category
}
```

### TestimonialType

Represents a customer testimonial.

```typescript
interface TestimonialType {
  name: string;               // Customer name
  location: string;           // Customer location
  text: string;               // Testimonial text
  rating: number;             // Rating (1-5)
  projectType: string;        // Type of project
  image?: string;             // Optional customer image
}
```

### ServiceArea

Represents a geographic service area.

```typescript
interface ServiceArea {
  city: string;               // City/area name
  distance: string;           // Distance from base (e.g., "0 km")
  isBase?: boolean;           // Whether this is the company's base
  description?: string;       // Optional description
  coordinates?: {             // Optional coordinates
    lat: number;
    lng: number;
  };
}
```

## Data Organization

### Projects Data

Projects are stored in `data/projects.ts` and include:

1. **Core Project Information**: ID, title, description
2. **Location Data**: Where the project was completed
3. **Temporal Data**: When the project was completed
4. **Categorization**: Category and tags for filtering
5. **Technical Details**: Size, duration, materials, features
6. **Customer Feedback**: Optional testimonial

Projects can be accessed and filtered through utility functions:

```typescript
// Get a specific project by ID
const project = getProjectById('modern-garden-royse');

// Get all unique project categories
const categories = getProjectCategories();

// Get all unique project locations
const locations = getProjectLocations();

// Get all unique project tags
const tags = getProjectTags();

// Filter projects by criteria
const filteredProjects = filterProjects('Belegningsstein', 'Røyse', 'modern');
```

### Services Data

Services are stored in `data/services.ts` and include:

1. **Core Service Information**: ID, title, description
2. **Visual Data**: Image URL
3. **Extended Information**: Optional long description
4. **Features**: List of service features/benefits
5. **Image Category**: Related category for image gallery

Services can be accessed through utility functions:

```typescript
// Get a specific service by ID
const service = getServiceById('belegningsstein');

// Get the image category for a service
const imageCategory = getServiceImageCategory('belegningsstein');

// Get service ID from project category
const serviceId = getServiceIdFromProjectCategory('Belegningsstein');
```

### Testimonials Data

Testimonials are stored in `data/testimonials.ts` and include:

1. **Customer Information**: Name, location
2. **Feedback**: Text and rating
3. **Project Context**: Type of project
4. **Visual Data**: Optional customer image

### Service Areas Data

Service areas are defined in constants and include:

1. **Location Information**: City name, distance from base
2. **Status**: Whether it's the company's base
3. **Description**: Optional additional information
4. **Coordinates**: Optional geographic coordinates

## Seasonal Mapping

A key aspect of the data structure is the seasonal mapping, which connects seasons to relevant content:

### Project Seasonal Mapping

```typescript
const SEASONAL_PROJECTS = {
  'vår': {
    categories: ['Hekk og Beplantning', 'Ferdigplen'],
    tags: ['beplantning', 'plen', 'hage']
  },
  'sommer': {
    categories: ['Platting', 'Cortenstål', 'Belegningsstein'],
    tags: ['terrasse', 'uteplass', 'innkjørsel']
  },
  'høst': {
    categories: ['Støttemur', 'Kantstein', 'Trapper og Repoer'],
    tags: ['terrengforming', 'støttemur', 'trapp']
  },
  'vinter': {
    categories: ['Cortenstål', 'Støttemur', 'Belegningsstein'],
    tags: ['planlegging', 'design']
  }
};
```

### Service Seasonal Mapping

```typescript
const SEASONAL_SERVICES = {
  'vår': {
    categories: ['Hekk', 'Ferdigplen', 'Beplantning'],
    features: ['Planting', 'Vanning', 'Gjødsling', 'Jordarbeid']
  },
  'sommer': {
    categories: ['Platting', 'Cortenstål', 'Belegningsstein'],
    features: ['Vedlikehold', 'Vanning', 'Beplantning']
  },
  'høst': {
    categories: ['Støttemurer', 'Kantstein', 'Trapper'],
    features: ['Beskjæring', 'Drenering', 'Vinterklargjøring']
  },
  'vinter': {
    categories: ['Planlegging', 'Design', 'Prosjektering'],
    features: ['Planlegging', 'Design', 'Prosjektering']
  }
};
```

## Data Relationships

The data types are interconnected in several ways:

1. **Projects to Services**: Projects have a category that maps to a service
2. **Projects to Testimonials**: Projects can have associated testimonials
3. **Services to Image Categories**: Services map to image categories for galleries
4. **Projects and Services to Seasons**: Both map to seasons for seasonal content

These relationships enable:
- **Cross-referencing**: Finding related content
- **Filtering**: Narrowing down content by criteria
- **Seasonal Adaptation**: Showing relevant content based on the current season```


#### `hooks-and-utils.md`

```markdown
# Hooks and Utilities Documentation

## Custom Hooks

### useAnalytics
Tracks page views and user interactions.

```typescript
export const useAnalytics = () => {
  const location = useLocation();

  useEffect(() => {
    trackPageView(location.pathname + location.search);
  }, [location]);
};
```

### useProjects
Manages project data and loading state.

```typescript
export const useProjects = () => {
  const [projects, setProjects] = useState<ProjectType[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setProjects(recentProjects);
    setLoading(false);
  }, []);

  return { projects, loading };
};
```

### useServices
Manages service data and loading state.

```typescript
export const useServices = () => {
  const [items, setItems] = useState<ServiceType[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setItems(services);
    setLoading(false);
  }, []);

  return { services: items, loading };
};
```

### useForm
Handles form state, validation, and submission.

```typescript
export const useForm = <T extends Record<string, any>>({
  initialValues,
  validationRules,
  onSubmit
}: UseFormProps<T>) => {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<FormErrors<T>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form handling logic...

  return {
    values,
    errors,
    isSubmitting,
    handleChange,
    handleSubmit,
    reset
  };
};
```

### useMediaQuery
Detects if a media query matches.

```typescript
export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(() =>
    window.matchMedia(query).matches
  );

  useEffect(() => {
    const media = window.matchMedia(query);
    const listener = (e: MediaQueryListEvent) => setMatches(e.matches);

    media.addEventListener('change', listener);
    return () => media.removeEventListener('change', listener);
  }, [query]);

  return matches;
};
```

### useIntersectionObserver
Detects when an element enters the viewport.

```typescript
export const useIntersectionObserver = <T extends Element>({
  threshold = 0,
  root = null,
  rootMargin = '0px',
  freezeOnceVisible = false,
}: UseIntersectionObserverProps = {}): [RefObject<T>, boolean] => {
  const elementRef = useRef<T>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const element = elementRef.current;
    if (!element || (freezeOnceVisible && isVisible)) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold, root, rootMargin }
    );

    observer.observe(element);
    return () => observer.disconnect();
  }, [threshold, root, rootMargin, freezeOnceVisible, isVisible]);

  return [elementRef, isVisible];
};
```

## Utility Functions

### cn (classNames)
Combines class names with Tailwind CSS.

```typescript
import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
```

### getCurrentSeason
Determines the current season.

```typescript
export const getCurrentSeason = (): 'vinter' | 'vår' | 'sommer' | 'høst' => {
  const month = new Date().getMonth();
  if (month >= 2 && month <= 4) return 'vår';
  if (month >= 5 && month <= 7) return 'sommer';
  if (month >= 8 && month <= 10) return 'høst';
  return 'vinter';
};
```

### formatDate
Formats dates in Norwegian locale.

```typescript
export function formatDate(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('no-NO', {
    month: 'long',
    year: 'numeric'
  }).format(dateObj);
}
```

### slugify
Creates URL-friendly slugs.

```typescript
export function slugify(text: string): string {
  return text
    .toLowerCase()
    .replace(/æ/g, 'ae')
    .replace(/ø/g, 'o')
    .replace(/å/g, 'a')
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}
```

### validateEmail
Validates email addresses.

```typescript
export const validateEmail = (email: string): string | undefined => {
  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    return 'Ugyldig e-postadresse';
  }
};
```

### validatePhone
Validates Norwegian phone numbers.

```typescript
export const validatePhone = (phone: string): string | undefined => {
  if (!/^(\+47)?[2-9]\d{7}$/.test(phone.replace(/\s/g, ''))) {
    return 'Ugyldig telefonnummer (8 siffer)';
  }
};
```

### formatPhoneNumber
Formats Norwegian phone numbers.

```typescript
export function formatPhoneNumber(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 8) {
    return cleaned.replace(/(\d{3})(\d{2})(\d{3})/, '$1 $2 $3');
  }
  return phone;
}
```

### generateSEOMeta
Generates SEO metadata.

```typescript
export const generateSEOMeta = ({
  title,
  description = SITE_CONFIG.description,
  image = SITE_CONFIG.ogImage,
  type = 'website',
  path = ''
}: SEOProps) => {
  const fullTitle = title ? `${title} | ${SITE_CONFIG.name}` : SITE_CONFIG.name;
  const url = `${SITE_CONFIG.url}${path}`;

  return {
    title: fullTitle,
    meta: [
      {
        name: 'description',
        content: description
      },
      {
        property: 'og:title',
        content: fullTitle
      },
      // ... more meta tags
    ]
  };
};
``````


#### `responsive-design.md`

```markdown
# Responsive Design

## Overview

The Ringerike Landskap website is fully responsive, providing an optimal viewing experience across a wide range of devices, from mobile phones to desktop monitors. This is achieved through a combination of Tailwind CSS utility classes, custom responsive components, and media queries.

## Responsive Framework

### Tailwind CSS Breakpoints

The project uses Tailwind CSS's responsive breakpoints:

- **sm**: 640px and up
- **md**: 768px and up
- **lg**: 1024px and up
- **xl**: 1280px and up
- **2xl**: 1536px and up

These breakpoints are used throughout the application to create responsive layouts:

```jsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {/* Content */}
</div>
```

### Container Component

The Container component provides consistent width constraints and padding across different screen sizes:

```jsx
<Container size="lg" className="py-8 sm:py-12">
  {children}
</Container>
```

The Container component applies different max-widths based on the `size` prop:

```typescript
const sizes = {
  sm: 'max-w-3xl',
  md: 'max-w-5xl',
  lg: 'max-w-7xl',
  xl: 'max-w-[90rem]'
};
```

## Responsive Layouts

### Grid Layouts

Grid layouts adapt to different screen sizes:

```jsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {/* Grid items */}
</div>
```

This creates:
- A single column on mobile
- Two columns on medium screens
- Three columns on large screens

### Flex Layouts

Flex layouts are used for more dynamic arrangements:

```jsx
<div className="flex flex-col sm:flex-row gap-4 justify-center">
  <Button to="/kontakt" size="lg" className="group">
    Book gratis befaring
  </Button>
  <Button
    to="/hva-vi-gjor"
    variant="outline"
    size="lg"
    className="bg-white/10 backdrop-blur-sm border-white text-white hover:bg-white/20"
  >
    Se v√•re tjenester
  </Button>
</div>
```

This creates:
- A vertical stack on mobile
- A horizontal row on small screens and up

## Responsive Components

### Hero Component

The Hero component adapts its height and content layout based on screen size:

```jsx
<section
  className="relative min-h-[480px] sm:min-h-[580px] flex items-center justify-center text-white overflow-hidden"
  // ...
>
  {/* Content */}
</section>
```

- Minimum height of 480px on mobile
- Minimum height of 580px on small screens and up

### ProjectCard Component

The ProjectCard component adjusts its height based on screen size:

```jsx
<div className="h-[350px] md:h-[400px] lg:h-[450px]">
  <ProjectCard
    project={project}
    onProjectClick={handleProjectClick}
    showTestimonial={true}
  />
</div>
```

- 350px height on mobile
- 400px height on medium screens
- 450px height on large screens

### Header Component

The Header component includes a responsive navigation menu:

```jsx
{/* Desktop navigation */}
<nav className="hidden md:flex md:items-center md:space-x-8">
  {/* Navigation items */}
</nav>

{/* Mobile menu button */}
<button
  type="button"
  className="md:hidden rounded-md p-2 text-gray-700"
  onClick={() => setIsMenuOpen(!isMenuOpen)}
  aria-expanded={isMenuOpen}
  aria-controls="mobile-menu"
>
  {/* Button content */}
</button>

{/* Mobile menu */}
{isMenuOpen && (
  <div className="md:hidden" id="mobile-menu">
    {/* Mobile menu content */}
  </div>
)}
```

- Hidden desktop navigation and visible mobile menu button on mobile
- Visible desktop navigation and hidden mobile menu button on medium screens and up

## Responsive Typography

Typography scales based on screen size:

```jsx
<h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 tracking-tight">
  {title}
</h1>

<p className="text-lg sm:text-xl max-w-2xl mx-auto leading-relaxed mb-8 text-gray-100">
  {subtitle}
</p>
```

- Smaller font sizes on mobile
- Larger font sizes on larger screens
- Adjusted margins and padding

## Responsive Images

Images adapt to their containers:

```jsx
<img
  src={project.image}
  alt={project.title}
  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
  loading="lazy"
/>
```

- `w-full h-full object-cover` ensures the image fills its container while maintaining aspect ratio
- `loading="lazy"` defers loading of off-screen images

## Responsive UI Elements

### Buttons

Buttons adjust their size based on screen size:

```jsx
<Button
  to="/kontakt"
  variant="primary"
  size="lg"
  className="px-8 py-3"
>
  Kontakt oss for gratis befaring
</Button>
```

The size prop maps to different padding and font sizes:

```typescript
const sizeClasses = {
  sm: "text-sm py-1.5 px-3",
  md: "text-base py-2 px-4",
  lg: "text-lg py-2.5 px-5"
};
```

### Forms

Form elements are responsive:

```jsx
<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
  <input
    type="text"
    placeholder="Navn"
    className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
  />
  <input
    type="text"
    placeholder="Budsjett"
    className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
  />
</div>
```

- Single column on mobile
- Two columns on medium screens and up

## Media Queries

Custom media queries are used for more specific responsive behavior:

```typescript
// Custom hook for media queries
export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(() =>
    window.matchMedia(query).matches
  );

  useEffect(() => {
    const media = window.matchMedia(query);
    const listener = (e: MediaQueryListEvent) => setMatches(e.matches);

    media.addEventListener('change', listener);
    return () => media.removeEventListener('change', listener);
  }, [query]);

  return matches;
};
```

This hook is used in components like TestimonialsSlider to adjust the number of visible testimonials:

```jsx
useEffect(() => {
  const handleResize = () => {
    if (window.innerWidth >= 1024) {
      setItemsPerView(3);
    } else if (window.innerWidth >= 768) {
      setItemsPerView(2);
    } else {
      setItemsPerView(1);
    }
  };

  // Set initial value
  handleResize();

  // Add event listener
  window.addEventListener('resize', handleResize);

  // Clean up
  return () => window.removeEventListener('resize', handleResize);
}, []);
```

## Responsive Best Practices

1. **Mobile-First Approach**: Start with mobile layout and enhance for larger screens
2. **Fluid Typography**: Scale text based on screen size
3. **Flexible Images**: Ensure images adapt to their containers
4. **Touch-Friendly UI**: Larger touch targets on mobile
5. **Performance Optimization**: Lazy loading and optimized assets
6. **Testing**: Test on multiple devices and screen sizes
7. **Accessibility**: Ensure accessibility across all screen sizes```


#### `seasonal-adaptation.md`

```markdown
# Seasonal Adaptation

## Overview

A key feature of the Ringerike Landskap website is its ability to adapt content based on the current season. This creates a dynamic user experience that is relevant to the time of year and helps users find services and projects that are appropriate for the current season.

## Season Detection

The current season is determined by the `getCurrentSeason()` utility function:

```typescript
export const getCurrentSeason = (): 'vinter' | 'v친r' | 'sommer' | 'h칮st' => {
  const month = new Date().getMonth();
  if (month >= 2 && month <= 4) return 'v친r';
  if (month >= 5 && month <= 7) return 'sommer';
  if (month >= 8 && month <= 10) return 'h칮st';
  return 'vinter';
};
```

This function maps months to seasons:
- **Spring (V친r)**: March, April, May (months 2-4)
- **Summer (Sommer)**: June, July, August (months 5-7)
- **Fall (H칮st)**: September, October, November (months 8-10)
- **Winter (Vinter)**: December, January, February (months 11, 0, 1)

## Seasonal Content Mapping

Each season is associated with specific types of content:

### Project Categories and Tags

```typescript
const SEASONAL_PROJECTS = {
  'v친r': {
    categories: ['Hekk og Beplantning', 'Ferdigplen'],
    tags: ['beplantning', 'plen', 'hage']
  },
  'sommer': {
    categories: ['Platting', 'Cortenst친l', 'Belegningsstein'],
    tags: ['terrasse', 'uteplass', 'innkj칮rsel']
  },
  'h칮st': {
    categories: ['St칮ttemur', 'Kantstein', 'Trapper og Repoer'],
    tags: ['terrengforming', 'st칮ttemur', 'trapp']
  },
  'vinter': {
    categories: ['Cortenst친l', 'St칮ttemur', 'Belegningsstein'],
    tags: ['planlegging', 'design']
  }
};
```

### Service Categories and Features

```typescript
const SEASONAL_SERVICES = {
  'v친r': {
    categories: ['Hekk', 'Ferdigplen', 'Beplantning'],
    features: ['Planting', 'Vanning', 'Gj칮dsling', 'Jordarbeid']
  },
  'sommer': {
    categories: ['Platting', 'Cortenst친l', 'Belegningsstein'],
    features: ['Vedlikehold', 'Vanning', 'Beplantning']
  },
  'h칮st': {
    categories: ['St칮ttemurer', 'Kantstein', 'Trapper'],
    features: ['Beskj칝ring', 'Drenering', 'Vinterklargj칮ring']
  },
  'vinter': {
    categories: ['Planlegging', 'Design', 'Prosjektering'],
    features: ['Planlegging', 'Design', 'Prosjektering']
  }
};
```

## Seasonal UI Components

Several components adapt based on the current season:

### SeasonalCTA

The SeasonalCTA component displays different content based on the season:

```typescript
const seasonData = {
  spring: {
    title: "Planlegg for Ringerike-v친ren",
    description: "V친ren er perfekt for 친 planlegge neste sesongs hageprosjekter...",
    icon: "游꺔",
    services: [
      "Lokaltilpasset planlegging",
      "Terrengvurdering",
      "Klimatilpasset design",
    ],
    cta: "Book gratis befaring i Ringerike",
  },
  summer: {
    title: "Nyt sommeren i en vakker hage",
    description: "F친 mest mulig ut av sommeren med en velstelt og innbydende hage...",
    // ...
  },
  // Fall and winter data...
};
```

### SeasonalProjectsCarousel

This component filters projects based on the current season:

```typescript
// Filter projects based on current season
useEffect(() => {
  const currentSeason = getCurrentSeason();
  const seasonMapping = SEASONAL_MAPPING[currentSeason];

  // Filter projects by season-appropriate categories and tags
  const filtered = projects.filter(project => {
    // Check if project category matches any seasonal category
    const categoryMatch = seasonMapping.categories.includes(project.category);

    // Check if any project tag matches seasonal tags
    const tagMatch = project.tags.some(tag =>
      seasonMapping.tags.includes(tag.toLowerCase())
    );

    return categoryMatch || tagMatch;
  });

  // If we don't have enough seasonal projects, add some recent ones
  if (filtered.length < 3) {
    // Get unique projects that aren't already in filtered
    const additionalProjects = projects
      .filter(p => !filtered.some(f => f.id === p.id))
      .slice(0, 3 - filtered.length);

    setSeasonalProjects([...filtered, ...additionalProjects]);
  } else {
    setSeasonalProjects(filtered);
  }
}, [projects]);
```

### SeasonalServicesSection

This component displays services relevant to the current season:

```typescript
// Filter services based on current season
const seasonalServices = services.filter(service => {
  // Check if service category matches any seasonal category
  const categoryMatch = seasonMapping.categories.some(category =>
    service.title.toLowerCase().includes(category.toLowerCase())
  );

  // Check if any service feature matches seasonal features
  const featureMatch = service.features?.some(feature =>
    seasonMapping.features.some(seasonFeature =>
      feature.toLowerCase().includes(seasonFeature.toLowerCase())
    )
  );

  return categoryMatch || featureMatch;
});
```

## Seasonal Hero Content

The home page hero section adapts based on the season:

```typescript
// Get season-appropriate hero image
const getSeasonalHeroImage = () => {
  const season = getCurrentSeason();
  switch (season) {
    case 'v친r':
      return '/images/site/hero-grass.webp';
    case 'sommer':
      return '/images/site/hero-main.webp';
    case 'h칮st':
      return '/images/site/hero-corten-steel.webp';
    case 'vinter':
      return '/images/site/hero-granite.webp';
    default:
      return '/images/site/hero-main.webp';
  }
};

// Get season-appropriate subtitle
const getSeasonalSubtitle = () => {
  const season = getCurrentSeason();
  switch (season) {
    case 'v친r':
      return 'V친ren er her! Som anleggsgartner med base p친 R칮yse skaper vi varige uterom...';
    // Other seasons...
  }
};
```

## Seasonal Filtering

Both the projects and services pages include seasonal filtering:

```typescript
// Season filter
<div>
  <label className="block text-sm font-medium text-gray-700 mb-2">
    <div className="flex items-center gap-1.5">
      <CalendarIcon className="w-4 h-4" />
      <span>Sesong</span>
    </div>
  </label>
  <select
    value={selectedSeason || ""}
    onChange={(e) =>
      setSelectedSeason(e.target.value || null)
    }
    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
  >
    <option value="">Alle sesonger</option>
    <option value="v친r">V친r</option>
    <option value="sommer">Sommer</option>
    <option value="h칮st">H칮st</option>
    <option value="vinter">Vinter</option>
  </select>
</div>
```

## Seasonal Tips

Both the projects and services pages include seasonal tips:

```typescript
{!selectedSeason && (
  <div className="mt-4 p-3 bg-green-50 border-l-4 border-green-500 rounded-r-md">
    <div className="flex items-center gap-2">
      <CalendarIcon className="w-5 h-5 text-green-600" />
      <p className="text-sm text-gray-700">
        <span className="font-medium">Tips:</span> {getSeasonDisplayName(currentSeason)} er en god tid for{' '}
        {SEASONAL_SERVICES[currentSeason as keyof typeof SEASONAL_SERVICES].categories.join(', ').toLowerCase()}.{' '}
        <button
          onClick={() => setSelectedSeason(currentSeason)}
          className="text-green-600 hover:underline font-medium"
        >
          Vis tjenester for {currentSeason}en
        </button>
      </p>
    </div>
  </div>
)}
```

## Benefits of Seasonal Adaptation

1. **Relevance**: Content is always relevant to the current time of year
2. **User Experience**: Users see content that is applicable to their current needs
3. **Marketing**: Seasonal promotions and recommendations
4. **Engagement**: Fresh content throughout the year
5. **SEO**: Seasonal keywords and content can improve search rankings

## Implementation Considerations

1. **Server vs. Client**: Season detection happens on the client side
2. **Caching**: Seasonal content should not be cached long-term
3. **Testing**: Test all seasonal variations
4. **Fallbacks**: Default content if seasonal content is not available
5. **Hemisphere**: The implementation assumes Northern Hemisphere seasons```


#### `sitemap.md`

```markdown
# Ringerike Landskap - Sitemap

## Main Navigation Structure
- **Home** (/)
  - Hero section with seasonal adaptation
  - Seasonal projects carousel
  - Service areas list
  - Seasonal services section
  - Testimonials section

- **Services** (/hva-vi-gjor)
  - Service filtering (Category, Function, Season)
  - Service listings with details
  - Benefits section (Lokalkunnskap, Tilpassede løsninger, Klimatilpasset)
  - CTA section

- **Projects** (/prosjekter)
  - Project filtering (Category, Location, Tag, Season)
  - Project grid with details
  - Seasonal recommendations

- **About Us** (/hvem-er-vi)
  - Company information
  - Team members
  - Core values and benefits

- **Contact** (/kontakt)
  - Contact form
  - Location information
  - Service areas

## Dynamic Routes
- **Service Detail** (/tjenester/:id)
  - Service description
  - Features list
  - Related projects
  - Image gallery
  - Contact CTA

- **Project Detail** (/prosjekter/:id)
  - Project description
  - Project specifications
  - Materials and features
  - Related service
  - Testimonial (if available)

## Additional Pages
- **Testimonials** (/kundehistorier)
  - Testimonial filtering
  - Testimonial grid
  - Testimonial categories
  - CTA section

- **Legal Pages**
  - Privacy Policy (/personvern)
  - Cookies Policy (/cookies)

## Service Areas
- Røyse (Main Base)
- Hønefoss
- Hole kommune
- Jevnaker
- Sundvollen
- Vik

## Seasonal Content Adaptation
- **Spring (Vår)**
  - Focus on: Hekk og Beplantning, Ferdigplen
  - Relevant tags: beplantning, plen, hage

- **Summer (Sommer)**
  - Focus on: Platting, Cortenstål, Belegningsstein
  - Relevant tags: terrasse, uteplass, innkjørsel

- **Fall (Høst)**
  - Focus on: Støttemurer, Kantstein, Trapper og Repoer
  - Relevant tags: terrengforming, støttemur, trapp

- **Winter (Vinter)**
  - Focus on: Planning and Design
  - Relevant tags: planlegging, design, prosjektering

## Service Categories
- Belegningsstein
- Cortenstål
- Støttemurer
- Platting
- Ferdigplen
- Kantstein
- Trapper og Repoer
- Hekk og Beplantning```


#### `techstack.md`

```markdown
<!-- todo -->```


#### `tldr.md`

```markdown
# Ringerike Landskap - TL;DR

## Core Architecture

```
src/
├── components/        # UI building blocks
│   ├── ui/            # Base components
│   ├── layout/        # Structure components
│   └── features/      # Feature-specific components
│
├── features/          # Business logic
│   ├── projects/      # Project-related features
│   ├── services/      # Service-related features
│   └── testimonials/  # Testimonial features
│
├── lib/               # Core utilities
│   ├── hooks/         # React hooks
│   ├── utils/         # Helper functions
│   └── types/         # TypeScript types
│
└── pages/             # Route components
```

## Key Concepts

1. **Seasonal Adaptation**
   - Content changes based on current season
   - Affects projects, services, and UI elements
   - Automatic detection and mapping

2. **Component Hierarchy**
   - UI Components → Feature Components → Page Components
   - Composition over inheritance
   - Reusable building blocks

3. **Data Flow**
   - Static data in `/data`
   - Props down, events up
   - Context for global state
   - Custom hooks for logic

4. **Responsive Design**
   - Mobile-first approach
   - Tailwind breakpoints
   - Fluid typography
   - Adaptive layouts

5. **Type Safety**
   - TypeScript throughout
   - Strict type checking
   - Interface-driven development

## Core Features

1. **Projects**
   - Filtering by category/location/season
   - Detailed views
   - Image galleries
   - Related services

2. **Services**
   - Seasonal recommendations
   - Feature highlights
   - Related projects
   - Contact CTAs

3. **Testimonials**
   - Rating system
   - Filtering
   - Seasonal relevance
   - Social proof

## Key Patterns

1. **Component Patterns**
   ```tsx
   <Container>
     <Hero />
     <ServiceGrid services={seasonalServices} />
     <TestimonialsSection />
   </Container>
   ```

2. **Hook Patterns**
   ```tsx
   const { projects } = useProjects();
   const isDesktop = useMediaQuery('(min-width: 1024px)');
   ```

3. **Data Patterns**
   ```typescript
   interface ProjectType {
     id: string;
     title: string;
     category: string;
     season: string;
   }
   ```

## Quick Reference

- **Seasonal Logic**: `getCurrentSeason()`
- **Routing**: React Router v6
- **Styling**: Tailwind CSS
- **State**: React Context + Hooks
- **Build**: Vite
- **Deployment**: Netlify

## File Naming

- Components: PascalCase (`Button.tsx`)
- Utilities: camelCase (`imageLoader.ts`)
- Types: PascalCase (`ProjectType.ts`)
- Hooks: camelCase (`useProjects.ts`)

## Best Practices

1. **Components**
   - Single responsibility
   - Prop validation
   - Error boundaries
   - Accessibility

2. **Performance**
   - Lazy loading
   - Image optimization
   - Code splitting
   - Memoization

3. **Maintenance**
   - Clear documentation
   - Consistent patterns
   - Type safety
   - Unit tests```
