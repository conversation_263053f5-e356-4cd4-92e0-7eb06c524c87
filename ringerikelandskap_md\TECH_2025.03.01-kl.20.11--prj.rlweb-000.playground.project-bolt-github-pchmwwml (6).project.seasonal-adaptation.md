# Seasonal Adaptation

## Overview

A key feature of the Ringerike Landskap website is its ability to adapt content based on the current season. This creates a dynamic user experience that is relevant to the time of year and helps users find services and projects that are appropriate for the current season.

## Season Detection

The current season is determined by the `getCurrentSeason()` utility function:

```typescript
export const getCurrentSeason = (): 'vinter' | 'vår' | 'sommer' | 'høst' => {
  const month = new Date().getMonth();
  if (month >= 2 && month <= 4) return 'vår';
  if (month >= 5 && month <= 7) return 'sommer';
  if (month >= 8 && month <= 10) return 'høst';
  return 'vinter';
};
```

This function maps months to seasons:
- **Spring (Vår)**: March, April, May (months 2-4)
- **Summer (Sommer)**: June, July, August (months 5-7)
- **Fall (Høst)**: September, October, November (months 8-10)
- **Winter (Vinter)**: December, January, February (months 11, 0, 1)

## Seasonal Content Mapping

Each season is associated with specific types of content:

### Project Categories and Tags

```typescript
const SEASONAL_PROJECTS = {
  'vår': {
    categories: ['Hekk og Beplantning', 'Ferdigplen'],
    tags: ['beplantning', 'plen', 'hage']
  },
  'sommer': {
    categories: ['Platting', 'Cortenstål', 'Belegningsstein'],
    tags: ['terrasse', 'uteplass', 'innkjørsel']
  },
  'høst': {
    categories: ['Støttemur', 'Kantstein', 'Trapper og Repoer'],
    tags: ['terrengforming', 'støttemur', 'trapp']
  },
  'vinter': {
    categories: ['Cortenstål', 'Støttemur', 'Belegningsstein'],
    tags: ['planlegging', 'design']
  }
};
```

### Service Categories and Features

```typescript
const SEASONAL_SERVICES = {
  'vår': {
    categories: ['Hekk', 'Ferdigplen', 'Beplantning'],
    features: ['Planting', 'Vanning', 'Gjødsling', 'Jordarbeid']
  },
  'sommer': {
    categories: ['Platting', 'Cortenstål', 'Belegningsstein'],
    features: ['Vedlikehold', 'Vanning', 'Beplantning']
  },
  'høst': {
    categories: ['Støttemurer', 'Kantstein', 'Trapper'],
    features: ['Beskjæring', 'Drenering', 'Vinterklargjøring']
  },
  'vinter': {
    categories: ['Planlegging', 'Design', 'Prosjektering'],
    features: ['Planlegging', 'Design', 'Prosjektering']
  }
};
```

## Seasonal UI Components

Several components adapt based on the current season:

### SeasonalCTA

The SeasonalCTA component displays different content based on the season:

```typescript
const seasonData = {
  spring: {
    title: "Planlegg for Ringerike-våren",
    description: "Våren er perfekt for å planlegge neste sesongs hageprosjekter...",
    icon: "🌱",
    services: [
      "Lokaltilpasset planlegging",
      "Terrengvurdering",
      "Klimatilpasset design",
    ],
    cta: "Book gratis befaring i Ringerike",
  },
  summer: {
    title: "Nyt sommeren i en vakker hage",
    description: "Få mest mulig ut av sommeren med en velstelt og innbydende hage...",
    // ...
  },
  // Fall and winter data...
};
```

### SeasonalProjectsCarousel

This component filters projects based on the current season:

```typescript
// Filter projects based on current season
useEffect(() => {
  const currentSeason = getCurrentSeason();
  const seasonMapping = SEASONAL_MAPPING[currentSeason];
  
  // Filter projects by season-appropriate categories and tags
  const filtered = projects.filter(project => {
    // Check if project category matches any seasonal category
    const categoryMatch = seasonMapping.categories.includes(project.category);
    
    // Check if any project tag matches seasonal tags
    const tagMatch = project.tags.some(tag => 
      seasonMapping.tags.includes(tag.toLowerCase())
    );
    
    return categoryMatch || tagMatch;
  });
  
  // If we don't have enough seasonal projects, add some recent ones
  if (filtered.length < 3) {
    // Get unique projects that aren't already in filtered
    const additionalProjects = projects
      .filter(p => !filtered.some(f => f.id === p.id))
      .slice(0, 3 - filtered.length);
    
    setSeasonalProjects([...filtered, ...additionalProjects]);
  } else {
    setSeasonalProjects(filtered);
  }
}, [projects]);
```

### SeasonalServicesSection

This component displays services relevant to the current season:

```typescript
// Filter services based on current season
const seasonalServices = services.filter(service => {
  // Check if service category matches any seasonal category
  const categoryMatch = seasonMapping.categories.some(category => 
    service.title.toLowerCase().includes(category.toLowerCase())
  );
  
  // Check if any service feature matches seasonal features
  const featureMatch = service.features?.some(feature => 
    seasonMapping.features.some(seasonFeature => 
      feature.toLowerCase().includes(seasonFeature.toLowerCase())
    )
  );
  
  return categoryMatch || featureMatch;
});
```

## Seasonal Hero Content

The home page hero section adapts based on the season:

```typescript
// Get season-appropriate hero image
const getSeasonalHeroImage = () => {
  const season = getCurrentSeason();
  switch (season) {
    case 'vår':
      return '/images/site/hero-grass.webp';
    case 'sommer':
      return '/images/site/hero-main.webp';
    case 'høst':
      return '/images/site/hero-corten-steel.webp';
    case 'vinter':
      return '/images/site/hero-granite.webp';
    default:
      return '/images/site/hero-main.webp';
  }
};

// Get season-appropriate subtitle
const getSeasonalSubtitle = () => {
  const season = getCurrentSeason();
  switch (season) {
    case 'vår':
      return 'Våren er her! Som anleggsgartner med base på Røyse skaper vi varige uterom...';
    // Other seasons...
  }
};
```

## Seasonal Filtering

Both the projects and services pages include seasonal filtering:

```typescript
// Season filter
<div>
  <label className="block text-sm font-medium text-gray-700 mb-2">
    <div className="flex items-center gap-1.5">
      <CalendarIcon className="w-4 h-4" />
      <span>Sesong</span>
    </div>
  </label>
  <select
    value={selectedSeason || ""}
    onChange={(e) =>
      setSelectedSeason(e.target.value || null)
    }
    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
  >
    <option value="">Alle sesonger</option>
    <option value="vår">Vår</option>
    <option value="sommer">Sommer</option>
    <option value="høst">Høst</option>
    <option value="vinter">Vinter</option>
  </select>
</div>
```

## Seasonal Tips

Both the projects and services pages include seasonal tips:

```typescript
{!selectedSeason && (
  <div className="mt-4 p-3 bg-green-50 border-l-4 border-green-500 rounded-r-md">
    <div className="flex items-center gap-2">
      <CalendarIcon className="w-5 h-5 text-green-600" />
      <p className="text-sm text-gray-700">
        <span className="font-medium">Tips:</span> {getSeasonDisplayName(currentSeason)} er en god tid for{' '}
        {SEASONAL_SERVICES[currentSeason as keyof typeof SEASONAL_SERVICES].categories.join(', ').toLowerCase()}.{' '}
        <button 
          onClick={() => setSelectedSeason(currentSeason)}
          className="text-green-600 hover:underline font-medium"
        >
          Vis tjenester for {currentSeason}en
        </button>
      </p>
    </div>
  </div>
)}
```

## Benefits of Seasonal Adaptation

1. **Relevance**: Content is always relevant to the current time of year
2. **User Experience**: Users see content that is applicable to their current needs
3. **Marketing**: Seasonal promotions and recommendations
4. **Engagement**: Fresh content throughout the year
5. **SEO**: Seasonal keywords and content can improve search rankings

## Implementation Considerations

1. **Server vs. Client**: Season detection happens on the client side
2. **Caching**: Seasonal content should not be cached long-term
3. **Testing**: Test all seasonal variations
4. **Fallbacks**: Default content if seasonal content is not available
5. **Hemisphere**: The implementation assumes Northern Hemisphere seasons