There are currently a lot of duplication related to the screenshot functionality, please resolve this optimally. Below is one possible approach to **consolidate everything** into a single script called `website-snapshot.js` that:

    1. **Captures screenshots on demand** using Puppeteer (no watchers or schedules).
    2. **Handles multiple pages** and multiple viewports (optional).
    3. **Stores snapshots** in time-stamped folders and keeps a `latest` reference if desired.

    This approach assumes:
    - You **already have a running server** on some port (e.g. `npm run dev` in a separate terminal).
    - You **don’t need** auto-watchers, dev-server triggers, or scheduling tasks—just **on-demand** captures.
    - You’d like to **keep** a “latest” folder in `screenshots/latest`, plus any number of older snapshot folders named by timestamp.
    - You’re comfortable having all logic self-contained in a single file so that you can remove `scripts/` if you wish.

    > **Tip**: If you want to start/stop the dev server automatically inside the script, you could adapt the code. But below is the simplest “on-demand” approach: **start your server separately**, then run `node website-snapshot.js`.

    ---

    ## 1. Proposed `website-snapshot.js`

    Create a new file at the project root (same level as `package.json`) named **`website-snapshot.js`**:

    ```js
    /**
     * website-snapshot.js
     *
     * A single on-demand script to capture website screenshots via Puppeteer.
     * Run: node website-snapshot.js
     */

    import fs from 'fs';
    import path from 'path';
    import { fileURLToPath } from 'url';
    import puppeteer from 'puppeteer';

    /* ------------------------------------------------------------------
       1. Setup Paths & Configuration
       ------------------------------------------------------------------ */
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);
    const projectRoot = __dirname; // or path.resolve(__dirname, '..') if desired

    // Where to save screenshots
    const SCREENSHOTS_DIR = path.join(projectRoot, 'screenshots');
    const LATEST_DIR = path.join(SCREENSHOTS_DIR, 'latest');

    // Pages you want to capture
    const PAGES = [
      { path: '/', name: 'home' },
      { path: '/hvem-er-vi', name: 'about' },
      { path: '/hva-vi-gjor', name: 'services' },
      { path: '/prosjekter', name: 'projects' },
      { path: '/kontakt', name: 'contact' },
    ];

    // Optional: multiple viewports if you want different device sizes
    const VIEWPORTS = [
      { width: 390, height: 844, name: 'mobile' },
      { width: 834, height: 1112, name: 'tablet' },
      { width: 1440, height: 900, name: 'desktop' },
    ];

    // The port where your dev server is running.
    // Make sure "npm run dev" (or similar) is started separately.
    const DEV_PORT = 5173;

    // If you want to wait a bit after loading each page for animations, etc.
    const CAPTURE_DELAY_MS = 2000;

    /* ------------------------------------------------------------------
       2. Directory Setup
       ------------------------------------------------------------------ */
    function ensureDirectory(dirPath) {
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }
    }

    // Ensure base screenshot directories exist
    ensureDirectory(SCREENSHOTS_DIR);
    ensureDirectory(LATEST_DIR);

    // Create a timestamp-based folder for each run
    function createTimestampedDir() {
      const now = new Date();
      const timestamp = now
        .toISOString()
        .replace(/T/, '_')
        .replace(/\..+/, '')
        .replace(/:/g, '-');
      // e.g. "2025-03-12_12-34-56"

      const snapshotDir = path.join(SCREENSHOTS_DIR, timestamp);
      ensureDirectory(snapshotDir);
      return snapshotDir;
    }

    /* ------------------------------------------------------------------
       3. Main Capture Logic
       ------------------------------------------------------------------ */
    async function capturePage(browser, snapshotDir, pagePath, pageName, viewport) {
      const url = `http://localhost:${DEV_PORT}${pagePath}`;
      const fileSuffix = `${pageName}-${viewport.name}.png`;

      try {
        // New page in the browser
        const page = await browser.newPage();
        await page.setViewport({ width: viewport.width, height: viewport.height });
        await page.goto(url, { waitUntil: 'networkidle2' });

        // Wait for any animations or dynamic content
        await new Promise((resolve) => setTimeout(resolve, CAPTURE_DELAY_MS));

        // Build final file paths
        const viewportDir = path.join(snapshotDir, viewport.name);
        ensureDirectory(viewportDir);

        const screenshotPath = path.join(viewportDir, fileSuffix);
        await page.screenshot({ path: screenshotPath, fullPage: true });

        // Also copy to "latest/<viewport>"
        const latestViewportDir = path.join(LATEST_DIR, viewport.name);
        ensureDirectory(latestViewportDir);
        const latestScreenshotPath = path.join(latestViewportDir, fileSuffix);
        fs.copyFileSync(screenshotPath, latestScreenshotPath);

        await page.close();
        console.log(`Captured: ${pageName} (${viewport.name})`);
      } catch (error) {
        console.error(`Error capturing ${pageName} (${viewport.name}):`, error);
      }
    }

    async function captureAllPages(snapshotDir) {
      // Launch Puppeteer once
      const browser = await puppeteer.launch();

      // For each viewport, for each page
      for (const vp of VIEWPORTS) {
        for (const pg of PAGES) {
          await capturePage(browser, snapshotDir, pg.path, pg.name, vp);
        }
      }

      await browser.close();
    }

    /* ------------------------------------------------------------------
       4. Run the Script
       ------------------------------------------------------------------ */
    async function main() {
      try {
        console.log('Starting screenshot capture...');
        const snapshotDir = createTimestampedDir();
        await captureAllPages(snapshotDir);
        console.log('\nAll screenshots captured successfully!\n');
        console.log(`- Timestamped folder: ${snapshotDir}`);
        console.log(`- Latest folder:       ${LATEST_DIR}`);
      } catch (err) {
        console.error('Snapshot process failed:', err);
        process.exit(1);
      }
    }

    // Invoke main if called directly (node website-snapshot.js)
    if (import.meta.url === `file://${process.argv[1]}`) {
      main();
    }
    ```

    ### How it Works
    1. **Configuration Block**
       - Defines which pages to capture (`PAGES`), which viewports to use (`VIEWPORTS`), and the port to connect to your local dev server.
    2. **Directory Setup**
       - Creates (if not exists) a `screenshots` folder in your project root, plus a `latest` subfolder.
       - Each run gets its own time-stamped subfolder, e.g. `screenshots/2025-03-12_12-34-56`.
    3. **Capture Loop**
       - Puppeteer is launched once.
       - For each viewport (mobile, tablet, desktop, etc.) and each page, it navigates to `http://localhost:<DEV_PORT><PAGE_PATH>`, waits, and saves a screenshot:
         - **Time-stamped folder** for permanent record (e.g. `screenshots/2025-03-12_12-34-56/mobile/home-mobile.png`).
         - **Copied to** `screenshots/latest/mobile/home-mobile.png` (the “latest” reference).
    4. **Final Output**
       - Logs the location of both your new time-stamped folder and the “latest” folder.

    > **Note**: We removed watchers, refresh steps, scheduling, metadata/AI analysis references, etc., to keep it **purely** on-demand. If you need those features, you can embed or adapt the relevant parts.

    ---

    ## 2. How to Use

    1. **Install Puppeteer** (if not already):
       ```bash
       npm install puppeteer
       ```

    2. **Ensure your dev server is running**
       - For example: `npm run dev` (assuming it starts on port 5173, matching `DEV_PORT` in the script).

    3. **Run the script**
       ```bash
       node website-snapshot.js
       ```

    4. **See the results**
       - A new folder `screenshots/<timestamp>` is created, containing subfolders for each viewport (mobile/tablet/desktop).
       - The script also updates `screenshots/latest/<viewport>/*` so you can always reference the most recent images.

    ---

    ## 3. Optional Tweaks

    - **Add to `package.json`**:
      ```json
      "scripts": {
        "snapshot": "node website-snapshot.js"
      }
      ```
      Then run with `npm run snapshot`.

    - **Cleaning old snapshots**:
      - If you still want to remove older time-stamped folders occasionally, you could add a small function in the same file that checks for directories older than X days and deletes them.

    - **Single vs. Multiple Viewports**:
      - If you only need one viewport, remove the `VIEWPORTS` array and just set a single `width/height`.

    - **Targeting Different Servers**:
      - If you have a staging or production URL, simply change the `url` to point externally (and remove or adjust `DEV_PORT`).

    - **Screen or DOM Interactions**:
      - If you need to click something or wait for specific elements, you can expand `capturePage()` with more Puppeteer logic (e.g., `await page.waitForSelector('.my-element')`).

    ---

    ## 4. Final Takeaway

    By placing all screenshot capture code into **one `website-snapshot.js`** script, you:

    - Eliminate the overhead of watchers, dev triggers, scheduling, or AI-oriented metadata.
    - Simplify the process to just **“run a single command when you need screenshots.”**
    - Maintain a straightforward, minimal maintenance approach.

    That’s it—**one file** controlling everything you need for quick, on-demand website snapshots.
