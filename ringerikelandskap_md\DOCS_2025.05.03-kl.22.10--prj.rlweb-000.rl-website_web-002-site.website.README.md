# Ringerike Landskap Website

This repository contains the website code for Ringerike Landskap, built using React, TypeScript, and Vite.

## Table of Contents

1. [Overview](#overview)
2. [Directory Structure](#directory-structure)
3. [Development](#development)
4. [Building the Website](#building-the-website)
5. [Deployment Guide](#deployment-guide)
   - [Prerequisites](#prerequisites)
   - [Apache Server](#apache-server)
   - [Nginx Server](#nginx-server)
   - [Vercel](#vercel)
   - [Netlify](#netlify)
   - [Manual Deployment](#manual-deployment)
6. [Environment Configuration](#environment-configuration)
7. [Import Standards](#import-standards)
8. [CSS and Styling](#css-and-styling)
9. [Using the Website Independently](#using-the-website-independently)
10. [Troubleshooting](#troubleshooting)
11. [Testing Deployment](#testing-deployment)

## Overview

The website is built using React, TypeScript, and Vite. It uses a component-based architecture with a focus on reusability and maintainability. This separation from development tools provides several benefits:

## Directory Structure

```
website/
├── config/           # Configuration files
│   └── env/          # Environment-specific configuration files
├── public/           # Static assets
│   ├── images/       # Images (categorized, site, team)
│   ├── .htaccess     # Apache configuration for SPA routing
│   └── _redirects    # Netlify configuration for SPA routing
├── scripts/          # Utility scripts
├── src/              # Source code
│   ├── app/          # Application root shell and router
│   ├── content/      # Content data (locations, projects, services, team, testimonials)
│   ├── data/         # Static data
│   ├── docs/         # Documentation
│   ├── layout/       # Shared layout components
│   ├── lib/          # Utility logic, API layer, config
│   │   ├── api/      # API client code
│   │   ├── config/   # Configuration
│   │   ├── context/  # React context providers
│   │   ├── hooks/    # Custom React hooks
│   │   ├── types/    # TypeScript definitions
│   │   └── utils/    # Utility functions
│   ├── sections/     # Chronologically ordered, self-contained sections
│   │   ├── 10-home/  # Home page and related components
│   │   ├── 20-about/ # About page and related components
│   │   ├── 30-services/ # Services pages and related components
│   │   ├── 40-projects/ # Projects pages and related components
│   │   ├── 50-testimonials/ # Testimonials pages and related components
│   │   └── 60-contact/ # Contact page and related components
│   ├── styles/       # Global styles
│   └── ui/           # Global, atomic UI components
├── package.json      # Project dependencies and scripts
├── tailwind.config.js # Tailwind CSS configuration
├── tsconfig.json     # TypeScript configuration
├── vercel.json       # Vercel configuration for SPA routing
└── vite.config.ts    # Vite configuration
```

## Development

To start the development server:

```bash
npm run dev
```

This will start the development server at http://localhost:5173.

## Building the Website

Before deploying, you need to build the website for the appropriate environment:

```bash
# Development build
npm run build

# Staging build
npm run build:staging

# Production build
npm run build:production
```

The built files will be in the `dist` directory with the following structure:

```
dist/
├── assets/       # JavaScript and CSS files
├── images/       # Optimized images
├── index.html    # Main HTML file
├── robots.txt    # Robots file
└── site.webmanifest # Web app manifest
```

## Deployment Guide

This section provides instructions for deploying the Ringerike Landskap website to different hosting environments, with a focus on ensuring proper routing for a single-page application (SPA).

### Prerequisites

Before deploying, make sure you have:

- Node.js (v16 or higher)
- npm (v7 or higher)
- Access to your hosting environment

### Apache Server

For Apache servers, the website includes a `.htaccess` file in the `public` directory that will be copied to the `dist` directory during build. This file contains the necessary rewrite rules for client-side routing.

1. Upload the contents of the `dist` directory to your web server.

2. Make sure your Apache server has `mod_rewrite` enabled and that `.htaccess` files are allowed to override server configuration:

   ```apache
   <Directory /path/to/your/website>
       Options Indexes FollowSymLinks
       AllowOverride All
       Require all granted
   </Directory>
   ```

3. If you're using a subdirectory (e.g., `example.com/ringerike/`), you'll need to update the `base` property in `vite.config.ts` before building:

   ```typescript
   base: '/ringerike/',
   ```

### Nginx Server

For Nginx servers, you'll need to add the following configuration to your server block:

```nginx
server {
    listen 80;
    server_name example.com;
    root /path/to/your/website;
    index index.html;

    # Serve static files directly
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|webp|woff|woff2|ttf|eot)$ {
        expires max;
        add_header Cache-Control "public, max-age=********";
    }

    # For client-side routing
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### Vercel

The website includes a `vercel.json` file with the necessary configuration for Vercel deployment:

1. Install the Vercel CLI:
   ```bash
   npm install -g vercel
   ```

2. Deploy to Vercel:
   ```bash
   vercel
   ```

### Netlify

The website includes a `_redirects` file in the `public` directory for Netlify deployment:

1. Create a `netlify.toml` file in the root directory:
   ```toml
   [build]
     publish = "dist"
     command = "npm run build:production"

   [[redirects]]
     from = "/*"
     to = "/index.html"
     status = 200
   ```

2. Deploy to Netlify:
   ```bash
   npx netlify-cli deploy
   ```

### Manual Deployment

For any static hosting service:

1. Build the website: `npm run build` (or `build:staging` or `build:production`)
2. Upload the contents of the `dist` directory to your hosting service

## Environment Configuration

The website uses environment-specific configuration files to handle different settings for development, staging, and production. These configurations are located in the `config/env` directory:

- `.env.development` - Development environment variables
- `.env.staging` - Staging environment variables
- `.env.production` - Production environment variables

Key environment variables include:

- `VITE_BASE_URL` - Base URL for the website
- `VITE_API_URL` - API URL
- `VITE_ENABLE_ANALYTICS` - Whether to enable analytics
- `VITE_DEBUG` - Whether to enable debug mode
- `VITE_FEATURE_*` - Feature flags

You can validate the environment configuration by running:

```bash
npm run validate-env
```

## Import Standards

This project follows specific import standards to ensure code consistency and maintainability.

### Core Principles

1. **Use absolute imports with the `@/` alias**
2. **Use barrel exports (index.ts files) for module exports**
3. **Group and organize imports consistently**

### Import Pattern

```typescript
// External dependencies (alphabetical order)
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

// Internal imports (grouped by module, alphabetical within groups)
import { Button, Card, Container } from '@/ui';
import { useData, useMediaQuery } from '@/lib/hooks';
import { formatDate } from '@/lib/utils';
import { ProjectType } from '@/lib/types';
```

### Barrel Exports

Each directory should have an `index.ts` file that exports its contents:

```typescript
// src/ui/index.ts
export { Button } from './Button';
export { Card } from './Card';
export { Container } from './Container';
// ...
```

### Import Rules

1. **No relative imports** with `../` or `../../`
2. **No direct imports** from implementation files when a barrel file exists
   - ❌ `import { Button } from '@/ui/Button';`
   - ✅ `import { Button } from '@/ui';`
3. **Exception**: It's acceptable to use relative imports within the same directory
   - ✅ `import { SubComponent } from './SubComponent';`

## CSS and Styling

The website uses Tailwind CSS for styling. The configuration is completely self-contained within the website directory:

- `tailwind.config.js` - Tailwind CSS configuration
- `postcss.config.js` - PostCSS configuration
- `src/index.css` - Main CSS file that imports Tailwind and other CSS files
- `src/styles/` - Directory containing additional CSS files:
  - `base.css` - Base styles
  - `utilities.css` - Utility classes
  - `animations.css` - Animation styles

When copying the website to a new location, make sure to install all dependencies including the Tailwind plugins:

```bash
npm install
```

## Using the Website Independently

This directory is designed to be self-contained and can be used independently of the parent project. Here's how to use it as a standalone project:

1. **Copy the directory**: Copy the entire `website` directory to a new location
2. **Install dependencies**: Run `npm install` in the new location
3. **Start development**: Run `npm run dev` to start the development server
4. **Build**: Run `npm run build` to build the website
5. **Preview build**: Run `npm run preview` to preview the built website

The build output will be in the `dist` directory within the website folder. All necessary configuration files are included in this directory, so it can function as a standalone project without any dependencies on the parent project.

## Troubleshooting

### 404 Errors on Page Refresh or Direct URL Access

If you're experiencing 404 errors when refreshing the page or accessing URLs directly, it's likely that the server is not properly configured to handle client-side routing.

1. Check that the `.htaccess` file (for Apache) or server configuration (for Nginx) is correctly set up.

2. Make sure the server is configured to allow `.htaccess` files to override server configuration.

### Blank Page or JavaScript Errors

If you're seeing a blank page or JavaScript errors:

1. Check the browser console for errors.

2. Verify that the `base` property in `vite.config.ts` is correctly set for your deployment environment.

3. Make sure all assets are being loaded correctly (check for 404 errors in the network tab).

## Testing Deployment

After deploying, test the following:

1. Direct URL access to different routes (e.g., `/prosjekter`, `/hva`, etc.).

2. Refreshing the page on different routes.

3. Navigation between pages using the application's links.

4. Check that all assets (images, CSS, JavaScript) are loading correctly.

If you encounter any issues, refer to the troubleshooting section or contact the development team.
