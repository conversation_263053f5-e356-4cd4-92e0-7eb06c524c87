# Memory Bank: Project Architecture Reference

The Memory Bank is the central architectural reference for the Ringerike Landskap Website project. It serves as a persistent abstraction layer that compresses the project's complexity into the minimal set of root-anchored abstractions.

## Purpose & Philosophy

The Memory Bank is not just documentation - it's a living memory of architectural intent and direction. It provides:

1. **Distilled Essence** - Core abstractions that define the project's purpose and structure
2. **Self-Describing Structure** - Clear organization that reinforces the project's architecture
3. **Consolidation Point** - A central reference to prevent structural drift and complexity bloat

## File Structure & Usage

| File | Purpose | When to Consult |
|------|---------|-----------------|
| `0-distilledContext.md` | Ultra-compressed project essence | When you need the highest-level understanding |
| `1-projectbrief.md` | Core business purpose and mission | When making product decisions |
| `2-productContext.md` | User needs, personas, and external realities | When designing features |
| `3-systemPatterns.md` | Component patterns and data flows | When implementing new components |
| `4-techContext.md` | Technical stack and constraints | When making technology decisions |
| `5-structureMap.md` | Current and target file structure | When reorganizing code |
| `6-activeContext.md` | In-progress refactorings and focus areas | When picking up ongoing work |
| `7-progress.md` | Completed architectural improvements | When reviewing milestones |
| `8-tasks.md` | Structure-anchored actionable items | When planning next steps |

## How to Use This Directory

### For New Team Members

1. Start with `0-distilledContext.md` to understand the project essence
2. Read `1-projectbrief.md` and `2-productContext.md` to grasp the business purpose
3. Review `5-structureMap.md` to understand the codebase organization
4. Check `8-tasks.md` to find starting points for contribution

### For Ongoing Development

1. Before adding new code, consult `3-systemPatterns.md` to ensure alignment with patterns
2. Check `6-activeContext.md` to understand current architectural priorities
3. Update `7-progress.md` when completing architectural improvements
4. Add to `8-tasks.md` when identifying new structure-improving tasks

### For Architectural Decisions

1. Always verify decisions against `0-distilledContext.md` and `1-projectbrief.md`
2. Document new patterns in `3-systemPatterns.md`
3. Update `5-structureMap.md` when changing file structure
4. Record completed improvements in `7-progress.md`

## Maintenance Guidelines

1. **Compression Over Expansion** - Always seek to reduce complexity, not document it
2. **Root-First Thinking** - Every change should strengthen the core architectural purpose
3. **Document Decisions** - Record the "why" behind structural changes
4. **Keep Current** - Update files as the architecture evolves

## Core Principles

1. **Type Safety** - All code must be properly typed with TypeScript
2. **Domain-First Organization** - Code is organized by business domain
3. **Feature Modularity** - Features are self-contained with clear interfaces
4. **Clean API Boundaries** - Data fetching is separated from UI components

Remember: The Memory Bank is not an inventory of chaos, but a system of persistent abstraction and simplification toward the optimal project structure.
