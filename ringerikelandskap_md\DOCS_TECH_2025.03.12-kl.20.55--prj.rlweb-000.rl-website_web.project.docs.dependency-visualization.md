# Dependency Visualization System

This document explains the dependency visualization system for the project, which helps you understand and analyze the project's code dependencies.

## Overview

The dependency visualization system provides tools to:

1. Generate visualizations of code dependencies
2. Analyze module relationships
3. Identify important dependencies
4. Check for proper import connections

The system is designed to be intuitive, with simple npm commands for different visualization needs.

## Quick Start

### Basic Commands

| Command                | Description                                         |
| ---------------------- | --------------------------------------------------- |
| `npm run deps`         | Show available dependency commands                  |
| `npm run deps:flow`    | Generate and open the flow diagram                  |
| `npm run deps:d3`      | Generate and open the D3 graph visualization        |
| `npm run deps:bubble`  | Generate and open the bubble chart                  |
| `npm run deps:circle`  | Generate and open the circle packing visualization  |
| `npm run deps:all`     | Generate all visualizations and open the dashboard  |
| `npm run deps:analyze` | Analyze dependencies without generating visuals     |
| `npm run deps:audit`   | Run a complete dependency audit                     |
| `npm run deps:safe`    | Run visualizations with Graphviz availability check |
| `npm run deps:cleanup` | Clean up redundant files                            |

### Visualization Types

#### Flow Diagram

The flow diagram (`deps:flow`) shows module relationships in a hierarchical layout, making it easy to see the flow of dependencies from one module to another.

#### D3 Graph

The D3 graph (`deps:d3`) provides an interactive visualization where you can drag nodes, zoom in/out, and explore the dependency graph interactively.

#### Bubble Chart

The bubble chart (`deps:bubble`) visualizes modules as bubbles, with the size representing the number of connections.

#### Circle Packing

The circle packing visualization (`deps:circle`) organizes modules in nested circles based on their directory structure.

## Advanced Features

### Dependency Analysis

The `deps:analyze` command provides a detailed analysis of your project dependencies, including:

-   Breakdown of module types (components, hooks, pages, etc.)
-   Identification of most connected modules
-   Validation of important module connections

The analysis results are stored in `.depcruise/data/dependency-analysis.json`.

### Complete Audit

The `deps:audit` command performs a comprehensive dependency audit:

1. Generates dependency data
2. Fixes path aliases and import connections
3. Analyzes module relationships
4. Generates all visualizations
5. Creates a dashboard with links to all visualizations

### Special Handling for Path Aliases

The system automatically resolves path aliases like `@/components/` to their actual paths, ensuring that visualizations correctly represent the relationships between modules.

### Important Module Tracking

The system monitors connections to important modules (configured in `dependency-manager.js`), ensuring they are properly represented in the visualizations.

## Configuration

The main configuration for the dependency visualization system is in `scripts/visualizations/dependency-manager.js`, where you can modify:

-   Important modules to track
-   Visualization types
-   Output directories
-   Commands requiring Graphviz

## Troubleshooting

### Missing Visualizations

If some visualizations are missing, it might be due to:

1. Missing Graphviz installation (required for SVG-based graphs)
2. Directory access issues
3. Corrupt dependency data

Try running `npm run deps:clean` to reset the visualization directory, then run `npm run deps:audit` to regenerate everything.

### Path Resolution Issues

If modules show as disconnected, it might be due to:

1. Path alias configuration issues in `depcruise-config.cjs`
2. Import syntax that the system can't recognize

Try running `npm run deps:analyze` to check if the important modules are being detected correctly.

## Legacy Commands

The system maintains backward compatibility with older `depcruise:*` commands, but the newer `deps:*` commands are recommended as they provide improved path alias handling and more comprehensive analysis.
