# Ringerike Landskap Website - Architecture Memory Bank

## Project Essence

> A React/TypeScript web platform for Ringerike Landskap that showcases landscaping services through a domain-driven architecture with clear feature boundaries and optimized component structure.

## Architecture Visualization

```mermaid
graph TD
    Root["🏠 Project Root"] --> Types["📝 Types"]
    Root --> Features["🧩 Features"]
    Root --> Hooks["🪝 Hooks"]
    Root --> API["🔌 API"]
    Root --> Config["⚙️ Config"]
    Root --> Utils["🔧 Utils"]
    Root --> Pages["📄 Pages"]

    Types --> DomainTypes["Domain Types"]
    Types --> ComponentTypes["Component Types"]
    Types --> CommonTypes["Common Types"]

    Features --> CommonFeature["Common UI"]
    Features --> ProjectsFeature["Projects"]
    Features --> ServicesFeature["Services"]
    Features --> TestimonialsFeature["Testimonials"]
    Features --> LayoutFeature["Layout"]

    Hooks --> DomainHooks["Domain Hooks"]
    Hooks --> UIHooks["UI Hooks"]
    Hooks --> FormHooks["Form Hooks"]

    API --> APIClient["API Client"]
    API --> Endpoints["Domain Endpoints"]
```

## Quick Navigation

| Category | Purpose | Current Status |
|----------|---------|----------------|
| [📝 Project Essence](0-distilledContext.md) | Ultra-compressed core understanding | Complete |
| [🎯 Business Purpose](1-projectbrief.md) | Mission and success definition | Complete |
| [👥 Product Context](2-productContext.md) | User needs and external realities | Complete |
| [🏛️ System Patterns](3-systemPatterns.md) | Component hierarchy and data flow | Complete |
| [⚙️ Tech Stack](4-techContext.md) | Technology constraints and standards | Complete |
| [🗺️ Structure Map](5-structureMap.md) | Current and target file organization | Complete |
| [🔄 Active Work](6-activeContext.md) | In-progress refactorings | In Progress |
| [✅ Progress Tracking](7-progress.md) | Completed architectural improvements | Updated |
| [📋 Tasks](8-tasks.md) | Structure-anchored actionable items | In Progress |
| [📚 Usage Guide](README.md) | How to use and maintain this Memory Bank | Complete |

## Current Focus: Type System & Hook Consolidation

1. **Type System Migration** - Centralizing all types in a domain-driven structure
2. **Hook Consolidation** - Unifying hook implementations with consistent patterns
3. **Feature Organization** - Planning domain-centric feature structure

## Project Values

- **Type Safety** - All code properly typed with TypeScript
- **Domain-First** - Organization by business domain
- **Feature Modularity** - Self-contained features with clear interfaces
- **Clean API Boundaries** - Data fetching separated from UI components

## How to Use This Memory Bank

Start with [0-distilledContext.md](0-distilledContext.md) for the highest-level essence, then dive deeper into specific aspects through the numbered files. Use the [README.md](README.md) for detailed guidance on maintaining this architectural reference.

> "Memory Bank is NOT an inventory of chaos. It is a system of persistent abstraction and simplification toward the optimal structure."
