│   ├── robots.txt
│   ├── site.webmanifest
│   ├── sitemap.xml
│       ├── metadata.json
│   ├── .gitignore
│   ├── .gitkeep
│   ├── README.md
│   ├── screenshot-report.html
│   │   │   ├── about-desktop.html
│   │   │   ├── contact-desktop.html
│   │   │   ├── home-desktop.html
│   │   │   ├── projects-desktop.html
│   │   │   └── services-desktop.html
│   │   │   ├── about-mobile.html
│   │   │   ├── contact-mobile.html
│   │   │   ├── home-mobile.html
│   │   │   ├── projects-mobile.html
│   │   │   └── services-mobile.html
│   │       ├── about-tablet.html
│   │       ├── contact-tablet.html
│   │       ├── home-tablet.html
│   │       ├── projects-tablet.html
│   │       └── services-tablet.html
│   │       ├── about-mobile.html
│   │       ├── contact-mobile.html
│   │       ├── home-mobile.html
│   │       ├── projects-mobile.html
│   │       └── services-mobile.html
│   │       ├── about-mobile.html
│   │       ├── contact-mobile.html
│   │       ├── projects-mobile.html
│   │       └── services-mobile.html
│   │   │   ├── about-desktop.html
│   │   │   ├── contact-desktop.html
│   │   │   ├── home-desktop.html
│   │   │   ├── projects-desktop.html
│   │   │   └── services-desktop.html
│   │   │   ├── about-mobile.html
│   │   │   ├── contact-mobile.html
│   │   │   ├── home-mobile.html
│   │   │   ├── projects-mobile.html
│   │   │   └── services-mobile.html
│   │       ├── about-tablet.html
│   │       ├── contact-tablet.html
│   │       ├── home-tablet.html
│   │       ├── projects-tablet.html
│   │       └── services-tablet.html
│   │   │   ├── about-mobile.html
│   │   │   ├── contact-mobile.html
│   │   │   ├── home-mobile.html
│   │   │   ├── projects-mobile.html
│   │   │   └── services-mobile.html
│   │       ├── about-tablet.html
│   │       ├── home-tablet.html
│   │       ├── projects-tablet.html
│   │       └── services-tablet.html
│       ├── .gitkeep
│       │   ├── about-desktop.html
│       │   ├── contact-desktop.html
│       │   ├── home-desktop.html
│       │   ├── projects-desktop.html
│       │   └── services-desktop.html
│       │   ├── about-mobile.html
│       │   ├── contact-mobile.html
│       │   ├── home-mobile.html
│       │   ├── projects-mobile.html
│       │   └── services-mobile.html
│           ├── about-tablet.html
│           ├── contact-tablet.html
│           ├── home-tablet.html
│           ├── projects-tablet.html
│           └── services-tablet.html
│   ├── auto-snapshot.js
│   ├── cleanup-legacy-screenshots.js
│   ├── cleanup.js
│   ├── dev-with-snapshots.js
│   ├── refresh-screenshots.js
│   ├── run-capture.bat
│   ├── screenshot-manager.js
│   └── tidy-screenshots.js
    ├── App.tsx
    ├── index.css
    ├── index.html
    ├── main.tsx
    ├── vite-env.d.ts
    │   ├── layout.tsx
    │   └── page.tsx
    │   ├── ContactForm.tsx
    │   ├── Navbar.tsx
    │   ├── ServiceCard.tsx
    │   ├── index.ts
    │   │   │   └── CoverageArea.tsx
    │   │   │   └── Hero.tsx
    │   │   │   ├── ProjectCard.tsx
    │   │   │   └── ProjectsCarousel.tsx
    │   │   │   └── SeasonalPlanning.tsx
    │   │   │   ├── ServiceCard.tsx
    │   │   │   ├── ServiceFeature.tsx
    │   │   │   └── Services.tsx
    │   │       └── Testimonials.tsx
    │   │   ├── Button.tsx
    │   │   ├── Container.tsx
    │   │   ├── Hero.tsx
    │   │   ├── ImageGallery.tsx
    │   │   ├── LocalExpertise.tsx
    │   │   ├── LocalServiceArea.tsx
    │   │   ├── Logo.tsx
    │   │   ├── SeasonalCTA.tsx
    │   │   ├── ServiceAreaList.tsx
    │   │   └── WeatherAdaptedServices.tsx
    │   │   └── ContactForm.tsx
    │   │   ├── Footer.tsx
    │   │   ├── Header.tsx
    │   │   ├── Hero.tsx
    │   │   ├── Layout.tsx
    │   │   ├── Meta.tsx
    │   │   └── Navbar.tsx
    │   │   ├── SeasonalGuide.tsx
    │   │   ├── ServiceAreaMap.tsx
    │   │   └── WeatherNotice.tsx
    │   │   ├── ProjectCard.tsx
    │   │   ├── ProjectFilter.tsx
    │   │   ├── ProjectGallery.tsx
    │   │   └── ProjectGrid.tsx
    │   │   └── TestimonialsSchema.tsx
    │   │   ├── Gallery.tsx
    │   │   └── ServiceCard.tsx
    │   │   ├── ErrorBoundary.tsx
    │   │   │   ├── Card.tsx
    │   │   │   ├── Icon.tsx
    │   │   │   ├── Image.tsx
    │   │   │   ├── Link.tsx
    │   │   │   ├── Loading.tsx
    │   │   │   ├── index.ts
    │   │   │       ├── Input.tsx
    │   │   │       ├── Select.tsx
    │   │   │       ├── Textarea.tsx
    │   │   │       └── index.ts
    │   │       ├── Layout.tsx
    │   │       └── index.ts
    │       ├── Button.tsx
    │       ├── Container.tsx
    │       ├── Hero.tsx
    │       ├── Intersection.tsx
    │       ├── Logo.tsx
    │       ├── Notifications.tsx
    │       ├── SeasonalCTA.tsx
    │       ├── ServiceAreaList.tsx
    │       ├── Skeleton.tsx
    │       ├── Transition.tsx
    │       └── index.ts
    │   ├── images.ts
    │   ├── routes.ts
    │   └── site.ts
    │   ├── index.ts
    │   ├── services.json
    │   ├── team.json
    │   │   └── index.ts
    │   │   └── index.ts
    │   │   └── index.ts
    │   │   └── index.ts
    │       └── index.ts
    │   ├── navigation.ts
    │   ├── projects.ts
    │   ├── services.ts
    │   ├── team.ts
    │   └── testimonials.ts
    │   ├── home.tsx
    │   ├── projects.tsx
    │   ├── services.tsx
    │   ├── team.tsx
    │   ├── testimonials.tsx
    │   │   ├── FilteredServicesSection.tsx
    │   │   ├── SeasonalProjectsCarousel.tsx
    │   │   ├── SeasonalServicesSection.tsx
    │   │   └── index.ts
    │   │   ├── ProjectCard.tsx
    │   │   ├── ProjectFilter.tsx
    │   │   ├── ProjectGallery.tsx
    │   │   ├── ProjectGrid.tsx
    │   │   ├── ProjectsCarousel.tsx
    │   │   ├── data.ts
    │   │   └── index.ts
    │   │   ├── ServiceCard.tsx
    │   │   ├── ServiceFeature.tsx
    │   │   ├── ServiceGrid.tsx
    │   │   ├── data.ts
    │   │   └── index.ts
    │       ├── AverageRating.tsx
    │       ├── Testimonial.tsx
    │       ├── TestimonialFilter.tsx
    │       ├── TestimonialSlider.tsx
    │       ├── TestimonialsSection.tsx
    │       ├── data.ts
    │       └── index.ts
    │   └── useData.ts
    │   ├── constants.ts
    │   ├── content.ts
    │   ├── hooks.ts
    │   ├── index.ts
    │   ├── types.ts
    │   ├── utils.ts
    │   │   └── index.ts
    │   │   ├── images.ts
    │   │   ├── index.ts
    │   │   ├── paths.ts
    │   │   └── site.ts
    │   │   └── index.ts
    │   │   └── AppContext.tsx
    │   │   ├── images.ts
    │   │   ├── index.ts
    │   │   ├── useAnalytics.ts
    │   │   ├── useDebounce.ts
    │   │   ├── useEventListener.ts
    │   │   ├── useForm.ts
    │   │   ├── useFormField.ts
    │   │   ├── useIntersectionObserver.ts
    │   │   ├── useLocalStorage.ts
    │   │   └── useMediaQuery.ts
    │   │   ├── common.ts
    │   │   ├── components.ts
    │   │   ├── content.ts
    │   │   └── index.ts
    │       ├── analytics.ts
    │       ├── date.ts
    │       ├── images.ts
    │       ├── index.ts
    │       ├── seo.ts
    │       └── validation.ts
    │   ├── AboutUs.tsx
    │   ├── ProjectDetail.tsx
    │   ├── Projects.tsx
    │   ├── ServiceDetail.tsx
    │   ├── Services.tsx
    │   ├── TestimonialsPage.tsx
    │   │   └── index.tsx
    │   │   └── index.tsx
    │   │   └── index.tsx
    │   │   ├── detail.tsx
    │   │   └── index.tsx
    │   │   ├── detail.tsx
    │   │   └── index.tsx
    │       └── index.tsx
    │   ├── animations.css
    │   ├── base.css
    │   └── utilities.css
        └── imageLoader.ts
