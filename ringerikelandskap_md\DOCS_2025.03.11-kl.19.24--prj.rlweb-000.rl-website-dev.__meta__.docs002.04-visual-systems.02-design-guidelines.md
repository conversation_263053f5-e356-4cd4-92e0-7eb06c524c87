# Ringerike Landskap Website Design Guidelines

## Introduction

This document defines the UI and UX guidelines for the Ringerike Landskap website, ensuring a cohesive, brand-aligned, and user-friendly digital experience. It serves as an implementation blueprint for developers and designers.

## Objectives

-   Provide a structured, intuitive interface that clearly presents services and projects
-   Ensure seamless navigation with a user journey that mirrors how customers discover and engage with Ringerike Landskap
-   Maintain brand identity through consistent typography, colors, and imagery
-   Optimize for performance, accessibility, and SEO

## User Experience (UX) & Information Architecture

The UX design is clean, direct, and conversion-driven, guiding visitors toward understanding services, exploring past work, and making contact. The structure is designed to be logical and easy to navigate.

### Navigation & Site Structure

The website is structured into five core sections, accessible via the main navigation bar:

-   **Hjem** (Home) – The landing page providing a broad overview and brand introduction
-   **Hva vi gjør** (What We Do / Services) – A detailed presentation of landscaping and related services
-   **Prosjekter** (Projects) – A portfolio/gallery of past projects showcasing work quality
-   **Hvem er vi** (Who We Are / About Us) – Information about the company, team, and values
-   **Kontakt** (Contact) – Contact information and a form for inquiries or quotes

### User Journey

- **Browsing Services**: Visitors land on the homepage or "Hva vi gjør" page to learn about the firm's core landscaping solutions.
- **Exploring Projects**: They can explore real examples under "Prosjekter," filtering results by interest—like building a new terrace or installing a støttemur.
- **Seasonal Tips**: The site subtly adapts to reflect current or upcoming seasons, guiding users to relevant services (e.g., planting ideas in spring).
- **Filtering**: Users filter projects by category or location to find relevant examples.
- **Social Proof**: Testimonials provide proof and build trust throughout the decision-making process.
- **Requesting a Quote**: When ready, they fill out the contact form or tap a "Book gratis befaring" button to schedule an on-site evaluation.
- **Personal Follow-Up**: The Ringerike Landskap team personally contacts clients to discuss specific needs, ensuring straightforward, no-frills communication.

## Visual Design System

### Color Palette

The color palette is centered around greens to reflect the landscaping focus, with complementary neutrals:

-   **Primary Green**: #1e9545 (Used for primary buttons, links, and accents)
-   **Dark Green**: #0d6b30 (Used for hover states and secondary elements)
-   **Light Green**: #e6f4ea (Used for backgrounds and subtle highlights)
-   **Neutral Dark**: #333333 (Used for primary text)
-   **Neutral Mid**: #767676 (Used for secondary text)
-   **Neutral Light**: #f5f5f5 (Used for backgrounds and dividers)
-   **White**: #ffffff (Used for backgrounds and text on dark colors)

### Typography

The website uses the Inter font family throughout:

-   **Headings**: Inter Bold (700)
    -   H1: 2.5rem (40px)
    -   H2: 2rem (32px)
    -   H3: 1.5rem (24px)
    -   H4: 1.25rem (20px)
-   **Body Text**: Inter Regular (400), 1rem (16px)
-   **Small Text/Captions**: Inter Regular (400), 0.875rem (14px)
-   **Buttons/CTAs**: Inter Medium (500), 1rem (16px)

### Imagery & Icons

-   **Photography**: High-quality images of completed landscaping projects, focusing on natural elements
-   **Icons**: Simple, line-based icons for UI elements (using Lucide React library)
-   **Illustrations**: Minimal use, primarily for decorative purposes

## User Interface Elements

### Visual Style
- Clean design with high-quality imagery showcasing landscaping work across prioritized services
- Green accents reflect nature and sustainability
- Balance of white space and content for readability

### Call-to-Actions
- Prominent buttons like "Book Gratis Befaring" should stand out visually to encourage conversions
- Consistent styling for all interactive elements
- Clear visual hierarchy to guide user attention

### Project Showcase
- Grid-style layout with filters for easy exploration of completed works 
- Projects categorized by job type or location
- High-quality images with consistent aspect ratios

## Seasonal Content Adaptation

The website content adapts based on the current season to highlight seasonally-relevant services and projects:

### Spring (Vår)
- **Focus on**: Hekk og Beplantning, Ferdigplen
- **Relevant tags**: beplantning, plen, hage
- **Visual cues**: Fresh greenery, new plantings, garden preparations

### Summer (Sommer)
- **Focus on**: Platting, Cortenstål, Belegningsstein
- **Relevant tags**: terrasse, uteplass, innkjørsel
- **Visual cues**: Outdoor living spaces, entertaining areas, completed projects

### Fall (Høst)
- **Focus on**: Støttemurer, Kantstein, Trapper og Repoer
- **Relevant tags**: terrengforming, støttemur, trapp
- **Visual cues**: Structural elements, terrain shaping, preparation for winter

### Winter (Vinter)
- **Focus on**: Planning and Design
- **Relevant tags**: planlegging, design, prosjektering
- **Visual cues**: Design sketches, planning elements, winter-ready landscapes

## Responsive Design Practices

- **Mobile-First Approach**: Start with mobile layout and enhance for larger screens
- **Fluid Typography**: Scale text based on screen size
- **Flexible Images**: Ensure images adapt to their containers
- **Touch-Friendly UI**: Larger touch targets on mobile
- **Performance Optimization**: Lazy loading and optimized assets
- **Testing**: Test on multiple devices and screen sizes
- **Accessibility**: Ensure accessibility across all screen sizes

## Project Categories & Locations

### Service Categories
- Belegningsstein
- Cortenstål
- Støttemurer
- Platting
- Ferdigplen
- Kantstein
- Trapper og Repoer
- Hekk og Beplantning

### Service Areas
- Røyse (Main Base)
- Hønefoss
- Hole kommune
- Jevnaker
- Sundvollen
- Vik

## Component Guidelines

### Buttons

-   **Primary Button**: Green background (#1e9545), white text, slight rounded corners (4px)
-   **Secondary Button**: White background, green border, green text
-   **Tertiary Button/Text Link**: No background, green text with subtle underline on hover

### Cards

-   **Service Cards**: Image background with gradient overlay, white text, subtle hover effect
-   **Project Cards**: Image with consistent aspect ratio, title and location information below

### Forms

-   **Input Fields**: Clear labels, light background, green focus state
-   **Validation**: Inline validation with clear error messages
-   **Submit Buttons**: Primary button style with loading state

## Responsive Design

The website follows a mobile-first approach with three main breakpoints:

-   **Mobile**: < 768px
-   **Tablet**: 768px - 1023px
-   **Desktop**: ≥ 1024px

Key responsive considerations:

-   Navigation collapses to hamburger menu on mobile
-   Grid layouts adjust from multi-column to single-column
-   Font sizes scale proportionally
-   Touch targets are appropriately sized for mobile

## Implementation Notes

-   Use Tailwind CSS for styling with custom theme configuration
-   Implement responsive images with appropriate sizing and formats
-   Ensure accessibility compliance with WCAG 2.1 standards
-   Optimize performance with lazy loading and code splitting
