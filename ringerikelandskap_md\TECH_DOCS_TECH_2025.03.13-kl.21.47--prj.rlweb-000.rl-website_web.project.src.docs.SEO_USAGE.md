# SEO Implementation Guide

This document outlines how to use the SEO utility and components in the Ringerike Landskap website.

## Overview

The website uses a powerful SEO setup with the following components:

1. **Meta Component**: A React component that adds all necessary SEO tags to the page
2. **SEO Utility**: A utility function that generates structured meta tags
3. **react-helmet-async**: For managing document head elements in React

## How to Use

### 1. On Page Components

Add SEO metadata to any page using the `Meta` component:

```tsx
import { Meta } from "@/components/layout/Meta";

const YourPage = () => {
    return (
        <>
            <Meta
                title="Your Page Title"
                description="A detailed description of your page (150-160 characters optimal)"
                image="/path/to/your/image.webp"
                keywords={["keyword1", "keyword2", "keyword3"]}
            />
            {/* Your page content */}
        </>
    );
};
```

### 2. Props Available

The `Meta` component accepts the following props:

| Prop          | Type                              | Description                 | Default               |
| ------------- | --------------------------------- | --------------------------- | --------------------- |
| `title`       | string                            | Page title                  | Site name             |
| `description` | string                            | Page description            | Site description      |
| `image`       | string                            | OG image path               | Site default image    |
| `type`        | "website" \| "article"            | Content type                | "website"             |
| `noindex`     | boolean                           | Whether to prevent indexing | false                 |
| `keywords`    | string[]                          | SEO keywords                | Site default keywords |
| `location`    | { city: string, region?: string } | Local business location     | undefined             |
| `schema`      | object                            | Custom schema.org JSON-LD   | undefined             |

### 3. For Service Area/Local Pages

For location-specific pages, use the location prop:

```tsx
<Meta title={`Anleggsgartner i ${city}`} location={{ city: "Hønefoss" }} />
```

This will automatically generate location-specific meta descriptions and schema markup.

### 4. Technical Implementation

Behind the scenes, the implementation:

1. Uses `react-helmet-async` to manage head tags
2. Generates optimized meta tags using the `generateSEOMeta` utility
3. Adds structured JSON-LD data for better search engine understanding

## Best Practices

1. **Keep titles under 60 characters** for best display in search results
2. **Meta descriptions should be 150-160 characters** and include keywords naturally
3. **Use high-quality, relevant images** (at least 1200x630px) for social sharing
4. **Include 3-5 relevant keywords** that accurately describe the page content
5. **Remember to update SEO metadata** when page content changes significantly

## Example

```tsx
<Meta
    title="Anleggsgartner i Ringerike"
    description="Profesjonell anleggsgartner med lang erfaring i Ringerike-regionen. Vi utfører alt fra belegningsstein til komplette hageanlegg med fokus på kvalitet og holdbarhet."
    image="/images/projects/ringerike-project.webp"
    keywords={["anleggsgartner", "ringerike", "hagearbeid", "belegningsstein"]}
    location={{ city: "Ringerike", region: "Viken" }}
/>
```

This setup ensures optimal visibility in search engines and proper display when shared on social media platforms.
