# Technical Context: Ringerike Landskap Website

## Stack Definition

### Frontend Core
- **Framework:** React 18.3+ with TypeScript 5.5+
- **Routing:** React Router DOM 6.x
- **Styling:** Tailwind CSS with composition utility (clsx/tailwind-merge)
- **Build System:** Vite 5.x
- **Animation:** Framer Motion for UI transitions

### Development Tooling
- **Linting:** ESLint 9.x with TypeScript integration
- **Dependency Analysis:** Dependency Cruiser for mapping dependencies
- **Code Quality:** TypeScript strict mode enabled
- **Browser Support:** Modern browsers (Chrome, Firefox, Safari, Edge)

## Technical Constraints

### Performance Requirements
- Initial load under 2s on 4G connection
- First Contentful Paint < 1.5s
- Time to Interactive < 3s
- Core Web Vitals compliance (LCP, FID, CLS)
- Responsive from 320px to 2560px viewport widths
- No render-blocking scripts

### Accessibility Standards
- WCAG 2.1 AA compliance required
- Proper semantic HTML structure
- Keyboard navigation support
- Screen reader compatibility
- Color contrast compliance 4.5:1 minimum

### SEO Requirements
- Proper meta tags for all pages
- Structured data for local business
- Structured data for testimonials/reviews
- Norwegian language optimization
- Local business schema integration
- Responsive images with appropriate dimensions

## Non-Negotiable Technical Principles

### 1. Type Safety
- All code must be properly typed with TypeScript
- No use of `any` type except in extreme cases with justification
- Types must accurately represent domain models
- Shared types must be exported from central location

### 2. Component Architecture
- Components must follow single responsibility principle
- Data fetching logic must be separate from rendering
- Presentational components must be pure functions where possible
- Component props must be explicitly typed

### 3. Performance Optimizations
- Images must be optimized (WebP format preferred)
- Code splitting for all routes
- Lazy loading for below-the-fold content
- No unnecessary re-renders
- Memoization for expensive calculations

### 4. Code Organization
- Feature-first organization
- Domain-driven file structure
- Clear and consistent naming conventions
- No circular dependencies
- Minimal third-party dependencies
