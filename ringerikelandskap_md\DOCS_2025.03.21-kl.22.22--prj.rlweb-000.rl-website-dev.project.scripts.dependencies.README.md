# Dependency Visualization Scripts

Scripts for analyzing and visualizing code dependencies in the project.

## Subdirectories

### `visualize/`
Scripts for creating different visualization formats for dependencies.

### `analyze/`
Scripts for analyzing dependencies without visualization.

### `utils/`
Helper utilities for dependency operations.

## Main Scripts

- `dependency-manager.js` - Central manager for all dependency operations
- Various visualization scripts for different formats (D3, bubble charts, circle packing, etc.)

## Usage

Most dependency operations can be performed through npm scripts:

```bash
# Generate all visualizations and open dashboard
npm run deps:all

# Generate quick visualizations
npm run deps:flow       # Flow diagram
npm run deps:bubble     # Bubble chart
npm run deps:d3         # D3 graph
npm run deps:circle     # Circle packing

# Analysis operations
npm run deps:analyze    # Analyze dependencies without visualization
npm run deps:audit      # Run complete dependency audit
