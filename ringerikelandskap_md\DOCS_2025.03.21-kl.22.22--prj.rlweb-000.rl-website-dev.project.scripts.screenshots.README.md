# Screenshot Scripts

Scripts for capturing and managing website screenshots.

## Subdirectories

### `capture/`
Scripts directly involved in capturing website screenshots.

### `manage/`
Scripts for managing, organizing, and cleaning screenshots.

## Main Scripts

- `screenshot-manager.js` - Central manager for all screenshot operations
- `auto-snapshot.js` - Captures website snapshots for AI analysis
- `capture-website.js` - Core script for capturing screenshots

## Usage

Most screenshot operations can be performed through the screenshot-manager using the appropriate command:

```bash
# Capture screenshots
npm run screenshots:capture

# Clean old screenshots
npm run screenshots:clean

# Tidy screenshot directory
npm run screenshots:tidy
