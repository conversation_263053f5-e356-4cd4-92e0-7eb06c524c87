<!-- 'https://www.perplexity.ai/search/popular-trends-in-2025-for-vis-cuYkqyljRyCCNO1j3j1ABA' -->

# Optimal Dependency-Cruiser Configuration for React Project Analysis

The following configuration provides a comprehensive setup for analyzing and visualizing React project dependencies, addressing cycle detection, hierarchical mapping, and supporting different project scales.

## File Structure

```
├── depcruise-config.js
├── rules.js
└── package.json
```

#### `depcruise-config.js`

```javascript
/** @type {import('dependency-cruiser').IConfiguration} */
module.exports = {
  forbidden: require('./rules').forbidden,
  allowed: require('./rules').allowed,
  options: {
    doNotFollow: {
      dependencyTypes: [
        "npm",
        "npm-dev",
        "npm-optional",
        "npm-peer",
        "npm-bundled",
        "npm-no-pkg",
      ],
    },

    // Focus on source code only
    includeOnly: "^src",

    // TypeScript support
    tsPreCompilationDeps: true,
    tsConfig: {
      fileName: "./tsconfig.json",
    },

    // Progress reporting
    progress: { type: "performance-log" },

    // Enhanced caching for large projects
    cache: {
      strategy: "metadata",
      compress: true,
    },

    // Visualization options
    reporterOptions: {
      dot: {
        theme: {
          graph: {
            rankdir: "LR",
            splines: "ortho",
          },
        },
      },
      archi: {
        // Collapse folders for clearer high-level view
        collapsePattern: "^src/[^/]+|^src/components/[^/]+|^src/features/[^/]+|^src/pages/[^/]+|^src/hooks/[^/]+|^src/utils/[^/]+",
        theme: {
          modules: [
            {
              criteria: { collapsed: true },
              attributes: { shape: "tab" },
            },
            {
              criteria: { source: "^src/components" },
              attributes: { fillcolor: "#CCFFCC" }, // Light green
            },
            {
              criteria: { source: "^src/features" },
              attributes: { fillcolor: "#FFCCCC" }, // Light red
            },
            {
              criteria: { source: "^src/pages" },
              attributes: { fillcolor: "#FFFFCC" }, // Light yellow
            },
            {
              criteria: { source: "^src/hooks" },
              attributes: { fillcolor: "#CCCCFF" }, // Light blue
            },
            {
              criteria: { source: "^src/utils" },
              attributes: { fillcolor: "#FFCCFF" }, // Light purple
            },
            {
              criteria: { source: "^src/state" },
              attributes: { fillcolor: "#CCFFFF" }, // Light cyan
            },
            {
              criteria: { circular: true },
              attributes: { style: "bold", color: "red", fontcolor: "red" },
            },
          ],
          graph: {
            splines: "ortho",
            rankdir: "TB",
            ranksep: "1.5",
            nodesep: "1.0",
          },
        },
      },
      mermaid: {
        // For PR visualization and documentation
        theme: "default",
        themeVariables: {
          primaryColor: "#e0f5ff",
          primaryTextColor: "#000",
          primaryBorderColor: "#00000033",
          lineColor: "#00000077",
        },
      },
    },
  },
};
```

#### `rules.js`

```javascript
module.exports = {
  forbidden: [
    {
      name: "no-circular",
      severity: "error",
      comment: "Circular dependencies are not allowed",
      from: {},
      to: {
        circular: true,
      },
    },
    {
      name: "no-orphans",
      severity: "warn",
      comment: "Orphaned modules should be removed",
      from: {
        orphan: true,
        pathNot: "\\.(spec|test)\\.(js|jsx|ts|tsx)$",
      },
      to: {},
    },
    {
      name: "no-unreachable-from-entry",
      severity: "error",
      comment: "Unreachable code should be removed",
      from: {
        path: "^src/(pages|app)/.+\\.(js|jsx|ts|tsx)$",
      },
      to: {
        path: "^src",
        pathNot: "node_modules|\\.(spec|test)\\.(js|jsx|ts|tsx)$",
        reachable: false,
      },
    },
    {
      name: "no-duplicate-patterns",
      severity: "warn",
      comment: "Similar import patterns may indicate code duplication",
      from: {},
      to: {
        moreThanOneDependencyType: true,
      },
    },
    {
      name: "no-unstable-dependencies",
      severity: "warn",
      comment: "More stable modules should not depend on less stable ones",
      from: {},
      to: {
        moreUnstable: true,
      },
    },
  ],
  allowed: [
    {
      name: "allow-specific-state-cycles",
      comment: "Allow specific circular dependencies for state management",
      from: {
        path: "^src/state/store\\.ts$",
      },
      to: {
        path: "^src/state/[^/]+/slice\\.ts$",
      },
    },
  ],
};
```

#### `package.json`

```json
{
  "scripts": {
    "depcruise:validate": "depcruise --validate --config depcruise-config.js src",
    "depcruise:graph": "depcruise --config depcruise-config.js --output-type dot src | dot -T svg > dependency-graph.svg",
    "depcruise:html": "depcruise --config depcruise-config.js --output-type dot src | dot -T svg | depcruise-wrap-stream-in-html > dependency-graph.html",
    "depcruise:archi": "depcruise --config depcruise-config.js --output-type archi src | dot -T svg | depcruise-wrap-stream-in-html > architecture-graph.html",
    "depcruise:circular": "depcruise --config depcruise-config.js --focus --rule-set forbidden=no-circular src | dot -T svg | depcruise-wrap-stream-in-html > circular-dependencies.html",
    "depcruise:orphans": "depcruise --config depcruise-config.js --focus --rule-set forbidden=no-orphans src | dot -T svg | depcruise-wrap-stream-in-html > orphaned-modules.html",
    "depcruise:pr": "depcruise --config depcruise-config.js --output-type mermaid --focus $(git diff --name-only HEAD) > changed-files-deps.md",
    "depcruise:component": "depcruise --config depcruise-config.js --focus"
  }
}
```

## Usage Guidelines

This configuration provides a comprehensive setup for analyzing React project dependencies with specific strengths:

### For Small to Medium Projects

Use the basic visualization and validation:
- `npm run depcruise:validate` - Check for dependency issues
- `npm run depcruise:html` - Generate an interactive HTML dependency graph
- `npm run depcruise:circular` - Focus specifically on circular dependencies

### For Large Complex Applications

Add hierarchical visualization and focused analysis:
- `npm run depcruise:archi` - Generate a high-level architectural view
- `npm run depcruise:component src/features/specificFeature` - Examine dependencies for a specific module
- `npm run depcruise:pr` - Visualize dependencies of changed files (ideal for code reviews)

### Advanced Features

The configuration includes:

1. **Cycle Detection**: Automatically highlights circular dependencies in red
2. **Hierarchical Mapping**: Collapses directories to show high-level architecture
3. **Color Coding**: Different sections of the codebase are visually distinguished
4. **Duplication Detection**: Rules to identify potential code duplication
5. **PR Integration**: Generate dependency graphs of changed files for code reviews
6. **Performance Optimization**: Caching for faster analysis of large codebases

This setup strikes a balance between ease of use and comprehensive analysis, suitable for teams of all sizes working on React projects.
