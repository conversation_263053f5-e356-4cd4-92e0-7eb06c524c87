# 'https://chat.deepseek.com/a/chat/s/9d3e3831-b997-49ac-aa8a-e961dddbdc32'

```
ringerike-landskap/
├── .ai-analysis/               # AI-specific visual data (auto-generated)
│   ├── latest/                 # Current AI analysis snapshots
│   └── snapshot-[timestamp]/   # Historical AI data
├── docs/                       # Project documentation
│   ├── 01-project-state/
│   ├── 02-codebase-map/
│   ├── 03-implementation-patterns/
│   ├── 04-visual-systems/
│   ├── 05-development-workflow/
│   ├── AI-README.md
│   └── README.md
├── scripts/                    # Critical screenshot system
│   ├── screenshot-manager.js
│   ├── dev-with-snapshots.js
│   └── auto-snapshot.js
├── src/
│   ├── app/                    # Core application setup
│   ├── components/
│   │   ├── core/               # Atomic UI components
│   │   │   ├── buttons/
│   │   │   ├── cards/
│   │   │   └── ...
│   │   ├── layout/             # Layout components
│   │   ├── sections/           # Page section components
│   │   └── templates/          # Page templates
│   ├── features/               # Feature-based components
│   │   ├── seasonal-display/
│   │   ├── project-gallery/
│   │   └── service-filtering/
│   ├── hooks/                  # Custom hooks
│   │   ├── use-seasonal.ts
│   │   └── use-viewport.js
│   ├── data/                   # Content management
│   │   ├── services.ts
│   │   ├── projects.ts
│   │   └── seasonal-content.ts
│   ├── lib/                    # Business logic
│   │   ├── content-generator/
│   │   └── image-optimizer/
│   ├── providers/              # State providers
│   │   ├── seasonal-provider.tsx
│   │   └── viewport-provider.tsx
│   ├── routes/                 # Routing configuration
│   │   └── app-router.tsx
│   ├── types/                  # Type definitions
│   │   └── models.ts
│   ├── utils/                  # Utilities
│   │   ├── accessibility/
│   │   └── performance/
│   └── workers/                # Web workers
│       └── image-processor.js
├── public/
│   ├── images/                 # Optimized assets
│   │   ├── projects/
│   │   ├── services/
│   │   └── seasonal/
│   └── locales/                # Localization
├── tests/                      # Testing infrastructure
│   ├── unit/
│   ├── integration/
│   └── visual-regression/
├── screenshots/                # Visual documentation
│   ├── latest/
│   └── [timestamped]/
└── config/                     # Tool configurations
    ├── tail
```
