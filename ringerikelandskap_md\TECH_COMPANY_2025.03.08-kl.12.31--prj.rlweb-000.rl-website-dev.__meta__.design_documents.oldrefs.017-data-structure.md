# Data Structure Documentation

## Core Data Types

### ProjectType

Represents a landscaping project with details about the work performed.

```typescript
interface ProjectType {
  id: string;                 // Unique identifier
  title: string;              // Project title
  description: string;        // Project description
  location: string;           // Location (city/area)
  completionDate: string;     // When the project was completed
  image: string;              // Main image URL
  category: string;           // Project category (e.g., "Belegningsstein")
  tags: string[];             // Related tags for filtering
  specifications: {
    size: string;             // Project size (e.g., "280m²")
    duration: string;         // Project duration (e.g., "6 uker")
    materials: string[];      // Materials used
    features: string[];       // Special features
  };
  testimonial?: {             // Optional testimonial
    quote: string;            // Customer quote
    author: string;           // Customer name
  };
}
```

### ServiceType

Represents a service offered by the company.

```typescript
interface ServiceType {
  id: string;                 // Unique identifier
  title: string;              // Service title
  description: string;        // Service description
  image: string;              // Main image URL
  longDescription?: string;   // Optional extended description
  features: string[];         // Service features/benefits
  imageCategory?: string;     // Related image category
}
```

### TestimonialType

Represents a customer testimonial.

```typescript
interface TestimonialType {
  name: string;               // Customer name
  location: string;           // Customer location
  text: string;               // Testimonial text
  rating: number;             // Rating (1-5)
  projectType: string;        // Type of project
  image?: string;             // Optional customer image
}
```

### ServiceArea

Represents a geographic service area.

```typescript
interface ServiceArea {
  city: string;               // City/area name
  distance: string;           // Distance from base (e.g., "0 km")
  isBase?: boolean;           // Whether this is the company's base
  description?: string;       // Optional description
  coordinates?: {             // Optional coordinates
    lat: number;
    lng: number;
  };
}
```

## Data Organization

### Projects Data

Projects are stored in `data/projects.ts` and include:

1. **Core Project Information**: ID, title, description
2. **Location Data**: Where the project was completed
3. **Temporal Data**: When the project was completed
4. **Categorization**: Category and tags for filtering
5. **Technical Details**: Size, duration, materials, features
6. **Customer Feedback**: Optional testimonial

Projects can be accessed and filtered through utility functions:

```typescript
// Get a specific project by ID
const project = getProjectById('modern-garden-royse');

// Get all unique project categories
const categories = getProjectCategories();

// Get all unique project locations
const locations = getProjectLocations();

// Get all unique project tags
const tags = getProjectTags();

// Filter projects by criteria
const filteredProjects = filterProjects('Belegningsstein', 'Røyse', 'modern');
```

### Services Data

Services are stored in `data/services.ts` and include:

1. **Core Service Information**: ID, title, description
2. **Visual Data**: Image URL
3. **Extended Information**: Optional long description
4. **Features**: List of service features/benefits
5. **Image Category**: Related category for image gallery

Services can be accessed through utility functions:

```typescript
// Get a specific service by ID
const service = getServiceById('belegningsstein');

// Get the image category for a service
const imageCategory = getServiceImageCategory('belegningsstein');

// Get service ID from project category
const serviceId = getServiceIdFromProjectCategory('Belegningsstein');
```

### Testimonials Data

Testimonials are stored in `data/testimonials.ts` and include:

1. **Customer Information**: Name, location
2. **Feedback**: Text and rating
3. **Project Context**: Type of project
4. **Visual Data**: Optional customer image

### Service Areas Data

Service areas are defined in constants and include:

1. **Location Information**: City name, distance from base
2. **Status**: Whether it's the company's base
3. **Description**: Optional additional information
4. **Coordinates**: Optional geographic coordinates

## Seasonal Mapping

A key aspect of the data structure is the seasonal mapping, which connects seasons to relevant content:

### Project Seasonal Mapping

```typescript
const SEASONAL_PROJECTS = {
  'vår': {
    categories: ['Hekk og Beplantning', 'Ferdigplen'],
    tags: ['beplantning', 'plen', 'hage']
  },
  'sommer': {
    categories: ['Platting', 'Cortenstål', 'Belegningsstein'],
    tags: ['terrasse', 'uteplass', 'innkjørsel']
  },
  'høst': {
    categories: ['Støttemur', 'Kantstein', 'Trapper og Repoer'],
    tags: ['terrengforming', 'støttemur', 'trapp']
  },
  'vinter': {
    categories: ['Cortenstål', 'Støttemur', 'Belegningsstein'],
    tags: ['planlegging', 'design']
  }
};
```

### Service Seasonal Mapping

```typescript
const SEASONAL_SERVICES = {
  'vår': {
    categories: ['Hekk', 'Ferdigplen', 'Beplantning'],
    features: ['Planting', 'Vanning', 'Gjødsling', 'Jordarbeid']
  },
  'sommer': {
    categories: ['Platting', 'Cortenstål', 'Belegningsstein'],
    features: ['Vedlikehold', 'Vanning', 'Beplantning']
  },
  'høst': {
    categories: ['Støttemurer', 'Kantstein', 'Trapper'],
    features: ['Beskjæring', 'Drenering', 'Vinterklargjøring']
  },
  'vinter': {
    categories: ['Planlegging', 'Design', 'Prosjektering'],
    features: ['Planlegging', 'Design', 'Prosjektering']
  }
};
```

## Data Relationships

The data types are interconnected in several ways:

1. **Projects to Services**: Projects have a category that maps to a service
2. **Projects to Testimonials**: Projects can have associated testimonials
3. **Services to Image Categories**: Services map to image categories for galleries
4. **Projects and Services to Seasons**: Both map to seasons for seasonal content

These relationships enable:
- **Cross-referencing**: Finding related content
- **Filtering**: Narrowing down content by criteria
- **Seasonal Adaptation**: Showing relevant content based on the current season