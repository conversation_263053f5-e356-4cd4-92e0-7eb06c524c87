# AI Analysis for Ringerike Landskap Website

This directory contains snapshots of the website for AI analysis. These snapshots allow AI assistants to see the rendered website and provide more accurate assistance when making code changes.

## How It Works

1. The system captures screenshots of key pages in the website
2. Screenshots are organized into timestamped snapshot directories
3. A metadata file tracks all snapshots and their information
4. **The latest screenshots are always available in the `latest` directory**
5. AI assistants can reference these screenshots when helping with code changes

## Directory Structure

-   **latest/**: Always contains the most recent screenshots (use this for consistent access)
-   **snapshot-[timestamp]/**: Historical snapshot directories
-   **metadata.json**: Information about all snapshots
-   **README.md**: This file

## Using Snapshots in Development

### Automatic Development with Snapshots

Run the development server with automatic snapshot capturing:

```bash
npm run dev:ai
```

This will:

-   Start the development server
-   Set up a file watcher to detect changes
-   Take snapshots automatically when changes are detected
-   Provide an HTTP endpoint to manually trigger snapshots
-   **Always update the `latest` directory with current screenshots**

### Manual Snapshot Capture

To manually capture a snapshot:

```bash
npm run ai:snapshot
```

### Referencing Snapshots in Conversations

When working with an AI assistant, you can reference the screenshots in two ways:

1. **Always use the latest screenshots** (recommended):

    ```
    /c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest
    ```

2. Reference a specific snapshot (if needed for comparison):
    ```
    /c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/snapshot-[timestamp]
    ```

## Configuration

The snapshot system is configured in `scripts/auto-snapshot.js` with the following settings:

-   Pages captured: Home, About, Services, Projects, Contact
-   Viewport size: 1440×900 (desktop)
-   Maximum snapshots kept: 10
-   Cooldown between snapshots: 30 seconds

## Benefits

-   AI assistants can see the actual rendered website
-   Visual context helps with UI/UX improvements
-   Better understanding of the current state of the application
-   More accurate assistance with styling and layout issues
-   **Consistent access to the latest screenshots via the `latest` directory**
