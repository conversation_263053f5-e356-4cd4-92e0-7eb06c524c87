# Target Implementation Patterns

This document outlines the recommended implementation patterns for the Ringerike Landskap website codebase. These patterns should be followed for all new code and when refactoring existing code.

## Component Patterns

### Functional Components with TypeScript

All components should use functional components with explicit TypeScript interfaces for props:

```tsx
interface ButtonProps {
  /** The content to be rendered inside the button */
  children: React.ReactNode;
  /** Button variant style */
  variant?: 'primary' | 'secondary' | 'outline';
  /** Function called when button is clicked */
  onClick?: () => void;
  /** Whether the button is disabled */
  isDisabled?: boolean;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Button component for user interactions
 */
function Button({
  children,
  variant = 'primary',
  onClick,
  isDisabled = false,
  className = '',
}: ButtonProps) {
  const baseClasses = 'px-4 py-2 rounded transition-colors';
  
  const variantClasses = {
    primary: 'bg-primary text-white hover:bg-primary-dark',
    secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300',
    outline: 'border border-primary text-primary hover:bg-primary-light'
  };
  
  return (
    <button 
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      onClick={onClick}
      disabled={isDisabled}
    >
      {children}
    </button>
  );
}

export default Button;
```

### Component Organization

Components should follow this organizational pattern:

1. Import statements
2. Type definitions
3. Constants and helper functions
4. Component definition
5. Export statement

Example:

```tsx
import { useState, useEffect } from 'react';
import { formatDate } from '@/utils/date';
import { ProjectType } from '@/types';

interface ProjectCardProps {
  project: ProjectType;
  isHighlighted?: boolean;
}

const MAX_DESCRIPTION_LENGTH = 150;

function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}

function ProjectCard({ project, isHighlighted = false }: ProjectCardProps) {
  const formattedDate = formatDate(project.completedDate);
  const truncatedDescription = truncateText(project.description, MAX_DESCRIPTION_LENGTH);
  
  return (
    <div className={`card ${isHighlighted ? 'bg-primary-light' : 'bg-white'}`}>
      <img src={project.image} alt={project.title} className="card-image" />
      <div className="card-content">
        <h3 className="card-title">{project.title}</h3>
        <p className="card-description">{truncatedDescription}</p>
        <span className="card-date">{formattedDate}</span>
      </div>
    </div>
  );
}

export default ProjectCard;
```

## State Management

### React Context for Shared State

Use React Context with custom hooks for shared state:

```tsx
// context.tsx
import { createContext, useContext, useState, ReactNode } from 'react';
import { ServiceType } from '@/types';
import { services as servicesData } from '@/data/services';

interface ServicesContextType {
  services: ServiceType[];
  filteredServices: ServiceType[];
  filterServices: (category: string) => void;
  isLoading: boolean;
}

const ServicesContext = createContext<ServicesContextType | undefined>(undefined);

export function ServicesProvider({ children }: { children: ReactNode }) {
  const [services] = useState<ServiceType[]>(servicesData);
  const [filteredServices, setFilteredServices] = useState<ServiceType[]>(servicesData);
  const [isLoading, setIsLoading] = useState(false);

  function filterServices(category: string) {
    setIsLoading(true);
    
    try {
      if (category === 'all') {
        setFilteredServices(services);
      } else {
        setFilteredServices(services.filter(service => service.category === category));
      }
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <ServicesContext.Provider value={{ 
      services, 
      filteredServices, 
      filterServices, 
      isLoading 
    }}>
      {children}
    </ServicesContext.Provider>
  );
}

// Custom hook for using the context
export function useServices() {
  const context = useContext(ServicesContext);
  
  if (context === undefined) {
    throw new Error('useServices must be used within a ServicesProvider');
  }
  
  return context;
}
```

### Local Component State

For component-specific state, use the useState hook with proper typing:

```tsx
function SearchForm() {
  const [query, setQuery] = useState<string>('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  
  // Rest of the component
}
```

## Styling Patterns

### Tailwind with Composition

Use Tailwind CSS with composition utilities:

```tsx
import { clsx } from 'clsx';

interface CardProps {
  title: string;
  content: string;
  variant?: 'default' | 'highlight' | 'muted';
  className?: string;
}

function Card({ title, content, variant = 'default', className }: CardProps) {
  const baseClasses = 'rounded-lg p-6 shadow-sm';
  
  const variantClasses = {
    default: 'bg-white',
    highlight: 'bg-primary-light border border-primary',
    muted: 'bg-gray-50 text-gray-700'
  };
  
  return (
    <div className={clsx(
      baseClasses,
      variantClasses[variant],
      className
    )}>
      <h3 className="text-xl font-bold mb-2">{title}</h3>
      <p>{content}</p>
    </div>
  );
}
```

### Responsive Design

Always implement responsive design using Tailwind's responsive modifiers:

```tsx
function ProjectGrid({ projects }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {projects.map(project => (
        <ProjectCard key={project.id} project={project} />
      ))}
    </div>
  );
}
```

## Data Management

### TypeScript Interfaces

Define comprehensive interfaces for all data structures:

```tsx
// types/project.ts
export interface ProjectType {
  id: string;
  title: string;
  description: string;
  image: string;
  category: string;
  location: string;
  completedDate: string;
  client?: string;
  features: string[];
  images?: string[];
  isFeatured?: boolean;
  seasonalRelevance?: ('summer' | 'winter' | 'spring' | 'autumn')[];
}
```

### Data Utilities

Create typed utility functions for data operations:

```tsx
// utils/projects.ts
import { ProjectType } from '@/types';
import { projects } from '@/data/projects';

/**
 * Returns a project by its ID
 */
export function getProjectById(id: string): ProjectType | undefined {
  return projects.find(project => project.id === id);
}

/**
 * Returns projects filtered by category
 */
export function getProjectsByCategory(category: string): ProjectType[] {
  if (category === 'all') return projects;
  return projects.filter(project => project.category === category);
}

/**
 * Returns featured projects
 */
export function getFeaturedProjects(limit?: number): ProjectType[] {
  const featured = projects.filter(project => project.isFeatured);
  return limit ? featured.slice(0, limit) : featured;
}

/**
 * Returns projects relevant for the current season
 */
export function getSeasonalProjects(): ProjectType[] {
  const currentMonth = new Date().getMonth();
  let currentSeason: 'winter' | 'spring' | 'summer' | 'autumn';
  
  if (currentMonth >= 2 && currentMonth <= 4) currentSeason = 'spring';
  else if (currentMonth >= 5 && currentMonth <= 7) currentSeason = 'summer';
  else if (currentMonth >= 8 && currentMonth <= 10) currentSeason = 'autumn';
  else currentSeason = 'winter';
  
  return projects.filter(project => 
    !project.seasonalRelevance || 
    project.seasonalRelevance.includes(currentSeason)
  );
}
```

## Error Handling

### Error Boundaries

Implement error boundaries for component error handling:

```tsx
import { Component, ErrorInfo, ReactNode } from 'react';

interface ErrorBoundaryProps {
  fallback?: ReactNode;
  children: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="error-boundary p-4 bg-red-50 text-red-800 rounded border border-red-300">
          <h2>Something went wrong.</h2>
          <p>{this.state.error?.message || 'Unknown error'}</p>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
```

### Try-Catch for Async Operations

Always use try-catch for async operations:

```tsx
async function fetchData() {
  try {
    setLoading(true);
    setError(null);
    
    const response = await fetch('/api/data');
    
    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }
    
    const data = await response.json();
    setData(data);
  } catch (err) {
    setError(err instanceof Error ? err : new Error('Unknown error'));
  } finally {
    setLoading(false);
  }
}
```

## Performance Optimizations

### React.memo for Pure Components

Use React.memo for components that render often but with the same props:

```tsx
import { memo } from 'react';

interface PriceTagProps {
  amount: number;
  currency?: string;
}

function PriceTag({ amount, currency = 'NOK' }: PriceTagProps) {
  const formattedPrice = new Intl.NumberFormat('nb-NO', {
    style: 'currency',
    currency,
  }).format(amount);
  
  return <span className="price">{formattedPrice}</span>;
}

export default memo(PriceTag);
```

### useMemo for Expensive Calculations

Use useMemo for expensive calculations:

```tsx
import { useMemo } from 'react';

function ProjectStats({ projects }) {
  const statistics = useMemo(() => {
    return {
      total: projects.length,
      featured: projects.filter(p => p.isFeatured).length,
      byCategory: projects.reduce((acc, project) => {
        acc[project.category] = (acc[project.category] || 0) + 1;
        return acc;
      }, {})
    };
  }, [projects]);
  
  return (
    <div className="stats">
      <p>Total Projects: {statistics.total}</p>
      <p>Featured: {statistics.featured}</p>
      {/* Rest of the component */}
    </div>
  );
}
```

### Lazy Loading for Routes

Use React.lazy and Suspense for route-based code splitting:

```tsx
import { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Loading from './components/ui/Loading';

// Lazy-loaded components
const HomePage = lazy(() => import('./pages/home'));
const AboutPage = lazy(() => import('./pages/about'));
const ServicesPage = lazy(() => import('./pages/services'));
const ProjectsPage = lazy(() => import('./pages/projects'));
const ContactPage = lazy(() => import('./pages/contact'));

function App() {
  return (
    <Router>
      <Suspense fallback={<Loading />}>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/hvem-er-vi" element={<AboutPage />} />
          <Route path="/hva-vi-gjor" element={<ServicesPage />} />
          <Route path="/prosjekter" element={<ProjectsPage />} />
          <Route path="/kontakt" element={<ContactPage />} />
        </Routes>
      </Suspense>
    </Router>
  );
}
```

## Testing

### Component Tests

Write unit tests for components:

```tsx
// Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import Button from './Button';

describe('Button component', () => {
  test('renders correctly with default props', () => {
    render(<Button>Click me</Button>);
    
    const button = screen.getByRole('button', { name: /click me/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('bg-primary');
    expect(button).not.toBeDisabled();
  });
  
  test('renders with different variants', () => {
    const { rerender } = render(<Button variant="primary">Primary</Button>);
    expect(screen.getByRole('button')).toHaveClass('bg-primary');
    
    rerender(<Button variant="secondary">Secondary</Button>);
    expect(screen.getByRole('button')).toHaveClass('bg-gray-200');
    
    rerender(<Button variant="outline">Outline</Button>);
    expect(screen.getByRole('button')).toHaveClass('border-primary');
  });
  
  test('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  test('is disabled when isDisabled is true', () => {
    render(<Button isDisabled>Disabled</Button>);
    expect(screen.getByRole('button')).toBeDisabled();
  });
});
```

By following these patterns, we can improve code quality, maintainability, and performance while ensuring a consistent development experience. 