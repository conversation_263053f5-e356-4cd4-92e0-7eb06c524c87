would these be suitable commands (in packages.json)?

    "depcruise:htm": "depcruise --include-only \"^src\" --output-type html src > dependency-graph.html"
    "depcruise:svg": "npx depcruise src --include-only \"^src\" --output-type dot | dot -T svg > dependency-graph.svg"
    "depcruise:archi": "depcruise --version && depcruise --config depcruise-config.js --output-type archi src | dot -T svg | depcruise-wrap-stream-in-html > high-level-dependencies.html"
