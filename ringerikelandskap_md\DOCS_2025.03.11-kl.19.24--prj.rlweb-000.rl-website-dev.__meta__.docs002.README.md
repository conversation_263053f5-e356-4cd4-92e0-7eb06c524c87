# Ringerike Landskap Website Documentation

This directory contains comprehensive documentation for the Ringerike Landskap website. The documentation is organized into logical sections to provide both high-level overviews and detailed technical recommendations.

## Table of Contents

-   [Documentation Overview](#documentation-overview)
-   [Key Files](#key-files)
-   [Project Documentation Folders](#project-documentation-folders)
-   [Key Systems](#key-systems)
-   [Using This Documentation](#using-this-documentation)
    -   [For Developers](#for-developers)
    -   [For AI Assistants](#for-ai-assistants)
-   [Screenshots](#screenshots)
-   [Contributing to Documentation](#contributing-to-documentation)
-   [Documentation Index](#documentation-index)

## Documentation Overview

The documentation is structured to support both human developers and AI assistants in understanding and working with the Ringerike Landskap website codebase. We've created a [Documentation Index](./00-documentation-index.md) that provides a comprehensive overview of all documentation with topical cross-references.

## Key Files

-   **[Documentation Index](./00-documentation-index.md)**: Comprehensive index of all documentation with cross-references
-   **[AI-README.md](./AI-README.md)**: Entry point for AI assistants with comprehensive context about the project, its architecture, and development patterns.
-   **[rl-website-initial-notes.md](./rl-website-initial-notes.md)**: Initial project documentation that provides detailed information about the company, target audience, website purpose, SEO strategy, and technical requirements. This document serves as the foundational brief for the entire project.

## Project Documentation Folders

1. **01-project-state/**: Documentation about the current state of the project

    - [01-current-codebase-reality.md](./01-project-state/01-current-codebase-reality.md): Assessment of the current codebase
    - [02-technical-debt-inventory.md](./01-project-state/02-technical-debt-inventory.md): Inventory of technical debt and issues
    - [03-company-information.md](./01-project-state/03-company-information.md): Information about Ringerike Landskap

2. **02-codebase-map/**: Documentation mapping the structure and relationships in the codebase

    - [01-directory-structure.md](./02-codebase-map/01-directory-structure.md): Explanation of the directory structure
    - [02-architecture-overview.md](./02-codebase-map/02-architecture-overview.md): Overview of the architecture
    - [03-component-hierarchy.md](./02-codebase-map/03-component-hierarchy.md): Hierarchy of components

3. **03-implementation-patterns/**: Documentation of current and target coding patterns

    - [01-current-patterns.md](./03-implementation-patterns/01-current-patterns.md): Current implementation patterns
    - [02-component-patterns.md](./03-implementation-patterns/02-component-patterns.md): Component implementation details
    - [03-target-patterns.md](./03-implementation-patterns/03-target-patterns.md): Target implementation patterns

4. **04-visual-systems/**: Documentation of visual systems

    - [01-screenshot-infrastructure.md](./04-visual-systems/01-screenshot-infrastructure.md): Infrastructure for capturing screenshots
    - [02-design-guidelines.md](./04-visual-systems/02-design-guidelines.md): Design guidelines
    - [03-styling-approach.md](./04-visual-systems/03-styling-approach.md): Approach to styling

5. **05-development-workflow/**: Documentation of development processes and requirements
    - [01-environment-setup.md](./05-development-workflow/01-environment-setup.md): Setting up the development environment
    - [02-optimization-guide.md](./05-development-workflow/02-optimization-guide.md): Performance optimization strategies
    - [03-accessibility-guide.md](./05-development-workflow/03-accessibility-guide.md): Accessibility best practices

**See also**: [Complete Documentation Index](./00-documentation-index.md) for topic-based cross-references

## Key Systems

The website is built around several integrated systems:

1. **Website Application**: A React-based frontend application utilizing TypeScript, Vite, Tailwind CSS, and React Router to create a fast, responsive, and user-friendly website.

2. **Visual Documentation System**: A system for capturing and organizing screenshots of the website to provide visual reference for development and track visual changes over time.

3. **AI Development Tools**: A set of documentation and tools that help AI assistants understand and work with the codebase more effectively.

4. **Content Management**: A system for managing website content through structured data files.

**See also**: [Architecture Overview](./02-codebase-map/02-architecture-overview.md), [Screenshot Infrastructure](./04-visual-systems/01-screenshot-infrastructure.md)

## Using This Documentation

### For Developers

1. Start by reading the [Documentation Index](./00-documentation-index.md) to get an overview of all available documentation.
2. Then read the [rl-website-initial-notes.md](./rl-website-initial-notes.md) file to understand the project's background, company information, and detailed requirements.
3. Next, review the files in the `01-project-state/` folder to understand the current state of the project.
4. Use the `02-codebase-map/` folder to understand the structure and architecture of the codebase.
5. Refer to the `03-implementation-patterns/` folder for guidance on how to implement features.
6. Check the `04-visual-systems/` folder for design guidelines and styling approaches.
7. Follow the guidance in the `05-development-workflow/` folder for development processes.

### For AI Assistants

AI assistants should start with the [AI-README.md](./AI-README.md) file, which provides comprehensive context about the project, followed by [rl-website-initial-notes.md](./rl-website-initial-notes.md) for the foundational project brief. The documentation structure provides a clear path for understanding the project and its requirements.

**See also**: [AI-README Reading Order](./AI-README.md#recommended-reading-order)

## Screenshots

The website screenshots are available in three locations:

1. **Historical Screenshots**: Located in timestamped directories under `/screenshots/`
2. **Latest Screenshots**: Always available at `/screenshots/latest/` for the most current view
3. **AI Analysis Screenshots**: Available at `/.ai-analysis/latest/`

To capture new screenshots, use one of the following commands:

```bash
# Capture screenshots manually
npm run screenshots:capture

# Start development server with automatic screenshot capturing
npm run dev:ai
```

**See also**: [Screenshot Infrastructure](./04-visual-systems/01-screenshot-infrastructure.md)

## Contributing to Documentation

When contributing to this documentation, please follow these guidelines:

1. Use clear, concise language
2. Include code examples where appropriate
3. Keep the documentation up-to-date with changes to the codebase
4. Follow the existing structure and naming conventions
5. Add appropriate cross-references to related documentation
6. Update the [Documentation Index](./00-documentation-index.md) when adding new documents

## Documentation Index

For a complete overview of all documentation with topical cross-references, see the [Documentation Index](./00-documentation-index.md).
