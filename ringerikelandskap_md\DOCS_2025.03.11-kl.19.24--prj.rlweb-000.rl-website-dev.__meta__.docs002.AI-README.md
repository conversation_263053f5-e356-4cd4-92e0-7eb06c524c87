# AI ASSISTANT GUIDE TO RINGERIKE LANDSKAP WEBSITE

## Table of Contents

-   [Critical Context](#critical-context-please-read-first)
-   [Project Overview](#project-overview)
-   [Key Systems](#key-systems)
-   [Documentation Structure](#documentation-structure)
-   [Known Limitations and Challenges](#known-limitations-and-challenges)
-   [Recommended Reading Order](#recommended-reading-order)
-   [Caution Areas](#caution-areas)
-   [Getting Started](#getting-started)
-   [Screenshot Reference](#screenshot-reference)
-   [Core Documentation Files](#core-documentation-files)
-   [Documentation Index](#documentation-index)

## CRITICAL CONTEXT (PLEASE READ FIRST)

This project is an in-development website for Ringerike Landskap AS that has evolved into a complex codebase. The documentation in this directory serves two purposes:

1. To accurately map the CURRENT state of the codebase (not an idealized version)
2. To establish a clear direction for future development

**See also**: [Current Codebase Reality](./01-project-state/01-current-codebase-reality.md), [Documentation Index](./00-documentation-index.md)

## PROJECT OVERVIEW

Ringerike Landskap AS is a landscaping and machine-contracting company based in Hole, Norway, offering eight core services including stonework, planting, steel installations, and retaining walls. This website is designed to showcase their services, projects, and expertise while adapting seasonally to highlight relevant offerings.

The website is built with:

-   React
-   TypeScript
-   Vite
-   Tailwind CSS
-   React Router
-   React Helmet for SEO management

**See also**: [Company Information](./01-project-state/03-company-information.md), [Initial Project Notes](./rl-website-initial-notes.md)

## KEY SYSTEMS

The project consists of four integrated systems:

1. **Website Application**: React/TypeScript site built with Vite, React Router, and Tailwind CSS
2. **Visual Documentation System**: Integrated screenshot capture system for tracking visual changes
3. **AI Development Tools**: Specialized infrastructure for AI-assisted development
4. **Content Management**: Typed content with seasonal adaptation

**See also**: [Architecture Overview](./02-codebase-map/02-architecture-overview.md), [Screenshot Infrastructure](./04-visual-systems/01-screenshot-infrastructure.md)

## DOCUMENTATION STRUCTURE

The documentation is organized into five main sections:

-   **01-project-state/**: Current state documentation

    -   [01-current-codebase-reality.md](./01-project-state/01-current-codebase-reality.md) - Start here
    -   [02-technical-debt-inventory.md](./01-project-state/02-technical-debt-inventory.md) - Issues to address
    -   [03-company-information.md](./01-project-state/03-company-information.md) - Business context

-   **02-codebase-map/**: Structure and relationships

    -   [01-directory-structure.md](./02-codebase-map/01-directory-structure.md) - File organization
    -   [02-architecture-overview.md](./02-codebase-map/02-architecture-overview.md) - System architecture
    -   [03-component-hierarchy.md](./02-codebase-map/03-component-hierarchy.md) - Component relationships

-   **03-implementation-patterns/**: Code patterns

    -   [01-current-patterns.md](./03-implementation-patterns/01-current-patterns.md) - Existing patterns
    -   [02-component-patterns.md](./03-implementation-patterns/02-component-patterns.md) - Component details
    -   [03-target-patterns.md](./03-implementation-patterns/03-target-patterns.md) - Target patterns

-   **04-visual-systems/**: Visual documentation

    -   [01-screenshot-infrastructure.md](./04-visual-systems/01-screenshot-infrastructure.md) - Critical system
    -   [02-design-guidelines.md](./04-visual-systems/02-design-guidelines.md) - Design principles
    -   [03-styling-approach.md](./04-visual-systems/03-styling-approach.md) - CSS approach

-   **05-development-workflow/**: Development process
    -   [01-environment-setup.md](./05-development-workflow/01-environment-setup.md) - Development environment
    -   [02-optimization-guide.md](./05-development-workflow/02-optimization-guide.md) - Performance
    -   [03-accessibility-guide.md](./05-development-workflow/03-accessibility-guide.md) - Accessibility

**See also**: [Complete Documentation Index](./00-documentation-index.md)

## KNOWN LIMITATIONS AND CHALLENGES

1. **Component Structure**: Inconsistent component structures exist
2. **State Management**: The implementation uses various state management approaches
3. **SEO Optimization**: The site needs further optimization for search engines
4. **Seasonal Content**: Website content should adapt to current seasons
5. **Performance**: Several areas need performance improvements

**See also**: [Technical Debt Inventory](./01-project-state/02-technical-debt-inventory.md)

## RECOMMENDED READING ORDER

When engaging with this codebase for the first time, AI assistants should follow this reading path:

1. First, read `rl-website-initial-notes.md` to understand the complete project background, company details, and website requirements.
2. Next, read the files in the `01-project-state/` folder to understand the current state of the project
3. Continue with the `02-codebase-map/` to understand the structure
4. Then review `03-implementation-patterns/` to learn current and target patterns
5. For visual understanding, check `04-visual-systems/`
6. Finally, understand the development process from `05-development-workflow/`

**See also**: [Documentation Index](./00-documentation-index.md) for quick navigation links

## CAUTION AREAS

When modifying this codebase, be especially careful with:

1. **Screenshot generation system** - This is essential for development workflow

    - See [04-visual-systems/01-screenshot-infrastructure.md](./04-visual-systems/01-screenshot-infrastructure.md)
    - Never modify core screenshot scripts without careful testing
    - Don't delete the `.ai-analysis` or `screenshots` directories

2. **Content data structures** - These must maintain their current shape

    - Content is defined in `src/data/*.ts` files
    - Maintain existing type structures and relationships

3. **Routing structure** - Navigation patterns should remain consistent
    - Routes are defined in App.tsx
    - Norwegian route names should be preserved

**See also**: [Screenshot Infrastructure](./04-visual-systems/01-screenshot-infrastructure.md), [Architecture Overview](./02-codebase-map/02-architecture-overview.md)

## GETTING STARTED

For quick setup:

```bash
npm install    # Install dependencies
npm run dev    # Start development server
npm run dev:ai # Start development with auto-screenshots
```

**See also**: [Environment Setup](./05-development-workflow/01-environment-setup.md)

## SCREENSHOT REFERENCE

To view the current visual state of the website, reference the latest screenshots at:

```
/.ai-analysis/latest/
```

These images are crucial for understanding the current visual state when making changes.

**See also**: [Screenshot Infrastructure](./04-visual-systems/01-screenshot-infrastructure.md)

## CORE DOCUMENTATION FILES

-   **[Documentation Index](./00-documentation-index.md)**: Complete overview of all documentation
-   **[README.md](./README.md)**: Main README file for human developers
-   **[AI-README.md](./AI-README.md)**: This file, specifically for AI assistants
-   **[Initial Project Notes](./rl-website-initial-notes.md)**: Initial project documentation with company information, website purpose, SEO strategy, and detailed requirements for the website.

## DOCUMENTATION INDEX

For a complete overview of all documentation and cross-referenced topics, see the [Documentation Index](./00-documentation-index.md).
